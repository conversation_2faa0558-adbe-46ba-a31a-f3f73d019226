import { BaseTool, ToolDefinition, ToolExecutionContext, ToolExecutionResult } from '../tools/base-tool.js';
export interface FunctionCall {
    name: string;
    arguments: Record<string, any>;
}
export interface ToolRegistryStats {
    totalTools: number;
    toolsByCategory: Record<string, number>;
    executionStats: Record<string, {
        totalExecutions: number;
        successfulExecutions: number;
        failedExecutions: number;
        averageExecutionTime: number;
    }>;
}
declare class FunctionRegistry {
    private tools;
    private categories;
    private executionStats;
    registerTool(tool: BaseTool, category?: string): void;
    unregisterTool(toolName: string): boolean;
    getTool(toolName: string): BaseTool | undefined;
    getAllTools(): BaseTool[];
    getToolsByCategory(category: string): BaseTool[];
    getCategories(): string[];
    getToolDefinitions(category?: string): ToolDefinition[];
    executeFunction(functionCall: FunctionCall, context: ToolExecutionContext): Promise<ToolExecutionResult>;
    executeFunctionSequence(functionCalls: FunctionCall[], context: ToolExecutionContext): Promise<ToolExecutionResult[]>;
    executeFunctionParallel(functionCalls: FunctionCall[], context: ToolExecutionContext): Promise<ToolExecutionResult[]>;
    searchTools(query: string): BaseTool[];
    suggestTools(context: ToolExecutionContext, intent?: string): BaseTool[];
    getStats(): ToolRegistryStats;
    generateDocumentation(): string;
    private updateExecutionStats;
    private isCriticalTool;
    private calculateRelevanceScore;
    clear(): void;
}
export declare const functionRegistry: FunctionRegistry;
export default functionRegistry;
//# sourceMappingURL=function-registry.d.ts.map