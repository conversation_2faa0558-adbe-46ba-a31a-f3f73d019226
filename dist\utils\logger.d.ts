import winston from 'winston';
declare class Logger {
    private logger;
    constructor();
    debug(message: string, meta?: any): void;
    info(message: string, meta?: any): void;
    warn(message: string, meta?: any): void;
    error(message: string, error?: Error | any): void;
    logApiCall(method: string, url: string, duration: number, status?: number): void;
    logToolExecution(toolName: string, parameters: any, duration: number, success: boolean): void;
    logWorkflowStep(stepName: string, stepData: any): void;
    logUserInteraction(command: string, parameters: any): void;
    setLevel(level: string): void;
    getLevel(): string;
    child(meta: any): winston.Logger;
}
export declare const logger: Logger;
export default logger;
//# sourceMappingURL=logger.d.ts.map