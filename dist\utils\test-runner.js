import chalk from 'chalk';
import { deepSeekClient } from '../core/ai-client.js';
import { functionRegistry } from '../core/function-registry.js';
import { sessionManager } from '../core/session-manager.js';
import { settings } from '../config/settings.js';
import { logger } from './logger.js';
export class TestRunner {
    testSuites = [];
    results = new Map();
    // Add a test suite
    addTestSuite(suite) {
        this.testSuites.push(suite);
    }
    // Run all tests
    async runAllTests() {
        console.log(chalk.cyan.bold('\n🧪 Running AI CLI Tool Test Suite\n'));
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;
        for (const suite of this.testSuites) {
            console.log(chalk.yellow.bold(`\n📋 ${suite.name}`));
            console.log(chalk.gray(suite.description));
            console.log();
            const suiteResults = [];
            for (const testCase of suite.tests) {
                totalTests++;
                const result = await this.runTest(testCase);
                suiteResults.push(result);
                if (result.passed) {
                    passedTests++;
                    console.log(`  ${chalk.green('✓')} ${testCase.name} ${chalk.gray(`(${result.duration}ms)`)}`);
                }
                else {
                    failedTests++;
                    console.log(`  ${chalk.red('✗')} ${testCase.name} ${chalk.gray(`(${result.duration}ms)`)}`);
                    console.log(`    ${chalk.red(result.message)}`);
                    if (result.error && settings.settings.ui.verbosity === 'debug') {
                        console.log(`    ${chalk.gray(result.error.stack)}`);
                    }
                }
            }
            this.results.set(suite.name, suiteResults);
        }
        // Summary
        console.log(chalk.cyan.bold('\n📊 Test Summary'));
        console.log(`Total Tests: ${totalTests}`);
        console.log(`${chalk.green('Passed:')} ${passedTests}`);
        console.log(`${chalk.red('Failed:')} ${failedTests}`);
        console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
        if (failedTests > 0) {
            console.log(chalk.red.bold('\n❌ Some tests failed. Check the output above for details.'));
        }
        else {
            console.log(chalk.green.bold('\n✅ All tests passed!'));
        }
    }
    // Run a specific test suite
    async runTestSuite(suiteName) {
        const suite = this.testSuites.find(s => s.name === suiteName);
        if (!suite) {
            throw new Error(`Test suite not found: ${suiteName}`);
        }
        console.log(chalk.cyan.bold(`\n🧪 Running Test Suite: ${suite.name}\n`));
        const suiteResults = [];
        let passedTests = 0;
        for (const testCase of suite.tests) {
            const result = await this.runTest(testCase);
            suiteResults.push(result);
            if (result.passed) {
                passedTests++;
                console.log(`  ${chalk.green('✓')} ${testCase.name} ${chalk.gray(`(${result.duration}ms)`)}`);
            }
            else {
                console.log(`  ${chalk.red('✗')} ${testCase.name} ${chalk.gray(`(${result.duration}ms)`)}`);
                console.log(`    ${chalk.red(result.message)}`);
            }
        }
        this.results.set(suite.name, suiteResults);
        console.log(`\n${chalk.green('Passed:')} ${passedTests}/${suite.tests.length}`);
    }
    // Run a single test
    async runTest(testCase) {
        const startTime = Date.now();
        const timeout = testCase.timeout || 30000;
        try {
            // Setup
            if (testCase.setup) {
                await testCase.setup();
            }
            // Run test with timeout
            const testPromise = testCase.test();
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Test timeout')), timeout);
            });
            const result = await Promise.race([testPromise, timeoutPromise]);
            result.duration = Date.now() - startTime;
            // Cleanup
            if (testCase.cleanup) {
                await testCase.cleanup();
            }
            return result;
        }
        catch (error) {
            const result = {
                passed: false,
                message: error instanceof Error ? error.message : String(error),
                duration: Date.now() - startTime,
                error: error instanceof Error ? error : new Error(String(error)),
            };
            // Cleanup on error
            if (testCase.cleanup) {
                try {
                    await testCase.cleanup();
                }
                catch (cleanupError) {
                    logger.warn('Test cleanup failed', cleanupError);
                }
            }
            return result;
        }
    }
    // Get test results
    getResults() {
        return this.results;
    }
    // Generate test report
    generateReport() {
        const report = [];
        report.push('# AI CLI Tool Test Report\n');
        report.push(`Generated: ${new Date().toISOString()}\n`);
        for (const [suiteName, results] of this.results.entries()) {
            const passed = results.filter(r => r.passed).length;
            const total = results.length;
            report.push(`## ${suiteName}`);
            report.push(`Passed: ${passed}/${total} (${Math.round((passed / total) * 100)}%)\n`);
            for (const result of results) {
                const status = result.passed ? '✅' : '❌';
                report.push(`- ${status} Test completed in ${result.duration}ms`);
                if (!result.passed) {
                    report.push(`  Error: ${result.message}`);
                }
                report.push('');
            }
        }
        return report.join('\n');
    }
}
// Create default test suites
export function createDefaultTestSuites() {
    return [
        {
            name: 'API Tests',
            description: 'Test DeepSeek API integration',
            tests: [
                {
                    name: 'API Key Validation',
                    description: 'Test if API key is valid',
                    category: 'api',
                    test: async () => {
                        const isValid = await deepSeekClient.validateApiKey();
                        return {
                            passed: isValid,
                            message: isValid ? 'API key is valid' : 'API key validation failed',
                            duration: 0,
                        };
                    },
                },
                {
                    name: 'Basic Chat Request',
                    description: 'Test basic chat functionality',
                    category: 'api',
                    test: async () => {
                        const response = await deepSeekClient.chat([
                            { role: 'user', content: 'Say "test successful"' }
                        ], { maxTokens: 10 });
                        const success = response.content.toLowerCase().includes('test');
                        return {
                            passed: success,
                            message: success ? 'Chat request successful' : 'Chat request failed',
                            duration: 0,
                            details: { response: response.content },
                        };
                    },
                },
                {
                    name: 'Reasoning Model Test',
                    description: 'Test reasoning model functionality',
                    category: 'api',
                    test: async () => {
                        const response = await deepSeekClient.chatWithReasoning([
                            { role: 'user', content: 'What is 2+2?' }
                        ], { maxTokens: 50 });
                        const hasReasoning = response.reasoning && response.reasoning.length > 0;
                        const hasCorrectAnswer = response.content.includes('4');
                        const testPassed = hasReasoning && hasCorrectAnswer;
                        return {
                            passed: testPassed || false,
                            message: testPassed
                                ? 'Reasoning model working correctly'
                                : 'Reasoning model test failed',
                            duration: 0,
                            details: {
                                reasoningSteps: response.reasoning?.length || 0,
                                content: response.content
                            },
                        };
                    },
                },
            ],
        },
        {
            name: 'Tool Tests',
            description: 'Test function calling and tool execution',
            tests: [
                {
                    name: 'Tool Registry',
                    description: 'Test tool registration and listing',
                    category: 'tools',
                    test: async () => {
                        const tools = functionRegistry.getAllTools();
                        const hasFileTools = tools.some(t => t.name.includes('file'));
                        const hasSystemTools = tools.some(t => t.name.includes('system'));
                        return {
                            passed: tools.length > 0 && hasFileTools && hasSystemTools,
                            message: `Found ${tools.length} tools`,
                            duration: 0,
                            details: { toolCount: tools.length, toolNames: tools.map(t => t.name) },
                        };
                    },
                },
                {
                    name: 'File Read Tool',
                    description: 'Test file reading functionality',
                    category: 'tools',
                    setup: async () => {
                        // Create a test file
                        const fs = await import('fs/promises');
                        await fs.writeFile('./test-file.txt', 'test content');
                    },
                    test: async () => {
                        const result = await functionRegistry.executeFunction({
                            name: 'file_read',
                            arguments: { path: './test-file.txt' }
                        }, {
                            sessionId: 'test',
                            workingDirectory: process.cwd(),
                            environment: {},
                            metadata: {},
                        });
                        return {
                            passed: result.success && result.result === 'test content',
                            message: result.success ? 'File read successful' : result.error || 'File read failed',
                            duration: result.duration,
                        };
                    },
                    cleanup: async () => {
                        // Clean up test file
                        const fs = await import('fs/promises');
                        try {
                            await fs.unlink('./test-file.txt');
                        }
                        catch (error) {
                            // Ignore cleanup errors
                        }
                    },
                },
            ],
        },
        {
            name: 'Session Tests',
            description: 'Test session management functionality',
            tests: [
                {
                    name: 'Session Creation',
                    description: 'Test creating and managing sessions',
                    category: 'session',
                    test: async () => {
                        const session = await sessionManager.createSession('Test Session', {
                            description: 'Test session for automated testing',
                        });
                        const testPassed = Boolean(session.id && session.name === 'Test Session');
                        return {
                            passed: testPassed,
                            message: session.id ? 'Session created successfully' : 'Session creation failed',
                            duration: 0,
                            details: { sessionId: session.id },
                        };
                    },
                },
                {
                    name: 'Message Management',
                    description: 'Test adding and retrieving messages',
                    category: 'session',
                    test: async () => {
                        const session = await sessionManager.getCurrentSession();
                        await sessionManager.addMessage({ role: 'user', content: 'test message' });
                        const messages = sessionManager.getSessionMessages(session.id, { limit: 1 });
                        const hasMessage = messages.length > 0 && messages[0].content === 'test message';
                        return {
                            passed: hasMessage,
                            message: hasMessage ? 'Message management working' : 'Message management failed',
                            duration: 0,
                        };
                    },
                },
            ],
        },
    ];
}
// Export default test runner instance
export const testRunner = new TestRunner();
// Initialize default test suites
createDefaultTestSuites().forEach(suite => testRunner.addTestSuite(suite));
//# sourceMappingURL=test-runner.js.map