import { ReasoningStep } from '../../core/ai-client.js';
export interface ReasoningDisplayOptions {
    showTimestamps: boolean;
    showStepNumbers: boolean;
    maxWidth: number;
    theme: 'dark' | 'light';
    realTime: boolean;
    compact: boolean;
}
export declare class ReasoningDisplay {
    private options;
    private steps;
    private isDisplaying;
    constructor(options?: Partial<ReasoningDisplayOptions>);
    addStep(step: ReasoningStep): void;
    displayStep(step: ReasoningStep): void;
    displayAllSteps(): void;
    private formatStep;
    private getTheme;
    private getHeader;
    private getFooter;
    private wrapText;
    private indentText;
    clear(): void;
    getStepCount(): number;
    getSteps(): ReasoningStep[];
    exportAsText(): string;
    exportAsJSON(): string;
    startStreaming(): void;
    stopStreaming(): void;
    updateOptions(options: Partial<ReasoningDisplayOptions>): void;
}
export declare function createReasoningDisplay(preset: 'verbose' | 'compact' | 'minimal'): ReasoningDisplay;
//# sourceMappingURL=reasoning-display.d.ts.map