export interface TestCase {
    name: string;
    description: string;
    category: 'api' | 'tools' | 'workflow' | 'session' | 'integration';
    timeout?: number;
    setup?: () => Promise<void>;
    test: () => Promise<TestResult>;
    cleanup?: () => Promise<void>;
}
export interface TestResult {
    passed: boolean;
    message: string;
    duration: number;
    details?: any;
    error?: Error;
}
export interface TestSuite {
    name: string;
    description: string;
    tests: TestCase[];
}
export declare class TestRunner {
    private testSuites;
    private results;
    addTestSuite(suite: TestSuite): void;
    runAllTests(): Promise<void>;
    runTestSuite(suiteName: string): Promise<void>;
    private runTest;
    getResults(): Map<string, TestResult[]>;
    generateReport(): string;
}
export declare function createDefaultTestSuites(): TestSuite[];
export declare const testRunner: TestRunner;
//# sourceMappingURL=test-runner.d.ts.map