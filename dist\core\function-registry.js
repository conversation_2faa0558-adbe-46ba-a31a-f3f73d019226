import { logger } from '../utils/logger.js';
class FunctionRegistry {
    tools = new Map();
    categories = new Map();
    executionStats = new Map();
    // Register a tool
    registerTool(tool, category = 'general') {
        if (this.tools.has(tool.name)) {
            logger.warn(`Tool ${tool.name} is already registered. Overwriting.`);
        }
        this.tools.set(tool.name, tool);
        // Add to category
        if (!this.categories.has(category)) {
            this.categories.set(category, []);
        }
        const categoryTools = this.categories.get(category);
        if (!categoryTools.includes(tool.name)) {
            categoryTools.push(tool.name);
        }
        // Initialize stats
        if (!this.executionStats.has(tool.name)) {
            this.executionStats.set(tool.name, {
                totalExecutions: 0,
                successfulExecutions: 0,
                failedExecutions: 0,
                totalExecutionTime: 0,
            });
        }
        logger.info(`Registered tool: ${tool.name} in category: ${category}`);
    }
    // Unregister a tool
    unregisterTool(toolName) {
        if (!this.tools.has(toolName)) {
            return false;
        }
        this.tools.delete(toolName);
        // Remove from categories
        for (const [category, tools] of this.categories.entries()) {
            const index = tools.indexOf(toolName);
            if (index > -1) {
                tools.splice(index, 1);
                if (tools.length === 0) {
                    this.categories.delete(category);
                }
                break;
            }
        }
        logger.info(`Unregistered tool: ${toolName}`);
        return true;
    }
    // Get a tool by name
    getTool(toolName) {
        return this.tools.get(toolName);
    }
    // Get all tools
    getAllTools() {
        return Array.from(this.tools.values());
    }
    // Get tools by category
    getToolsByCategory(category) {
        const toolNames = this.categories.get(category) || [];
        return toolNames.map(name => this.tools.get(name)).filter(Boolean);
    }
    // Get all categories
    getCategories() {
        return Array.from(this.categories.keys());
    }
    // Get tool definitions for AI model
    getToolDefinitions(category) {
        const tools = category ? this.getToolsByCategory(category) : this.getAllTools();
        return tools.map(tool => tool.getDefinition());
    }
    // Execute a function call
    async executeFunction(functionCall, context) {
        const tool = this.tools.get(functionCall.name);
        if (!tool) {
            const error = `Tool '${functionCall.name}' not found`;
            logger.error(error);
            return {
                success: false,
                error,
                duration: 0,
            };
        }
        // Check if tool can be executed in current context
        if (!tool.canExecute(context)) {
            const error = `Tool '${functionCall.name}' cannot be executed in current context`;
            logger.error(error);
            return {
                success: false,
                error,
                duration: 0,
            };
        }
        // Execute the tool
        const result = await tool.safeExecute(functionCall.arguments, context);
        // Update statistics
        this.updateExecutionStats(functionCall.name, result);
        return result;
    }
    // Execute multiple function calls in sequence
    async executeFunctionSequence(functionCalls, context) {
        const results = [];
        for (const functionCall of functionCalls) {
            const result = await this.executeFunction(functionCall, context);
            results.push(result);
            // Stop execution if a critical tool fails
            if (!result.success && this.isCriticalTool(functionCall.name)) {
                logger.warn(`Critical tool ${functionCall.name} failed, stopping sequence execution`);
                break;
            }
        }
        return results;
    }
    // Execute multiple function calls in parallel
    async executeFunctionParallel(functionCalls, context) {
        const promises = functionCalls.map(functionCall => this.executeFunction(functionCall, context));
        return Promise.all(promises);
    }
    // Search tools by name or description
    searchTools(query) {
        const lowerQuery = query.toLowerCase();
        return this.getAllTools().filter(tool => tool.name.toLowerCase().includes(lowerQuery) ||
            tool.description.toLowerCase().includes(lowerQuery));
    }
    // Get tool suggestions based on context
    suggestTools(context, intent) {
        // Simple suggestion algorithm - can be enhanced with ML
        const allTools = this.getAllTools();
        if (!intent) {
            return allTools.slice(0, 5); // Return first 5 tools
        }
        const intentLower = intent.toLowerCase();
        const scored = allTools.map(tool => ({
            tool,
            score: this.calculateRelevanceScore(tool, intentLower, context),
        }));
        return scored
            .sort((a, b) => b.score - a.score)
            .slice(0, 5)
            .map(item => item.tool);
    }
    // Get registry statistics
    getStats() {
        const toolsByCategory = {};
        for (const [category, tools] of this.categories.entries()) {
            toolsByCategory[category] = tools.length;
        }
        const executionStats = {};
        for (const [toolName, stats] of this.executionStats.entries()) {
            executionStats[toolName] = {
                totalExecutions: stats.totalExecutions,
                successfulExecutions: stats.successfulExecutions,
                failedExecutions: stats.failedExecutions,
                averageExecutionTime: stats.totalExecutions > 0
                    ? stats.totalExecutionTime / stats.totalExecutions
                    : 0,
            };
        }
        return {
            totalTools: this.tools.size,
            toolsByCategory,
            executionStats,
        };
    }
    // Generate comprehensive documentation
    generateDocumentation() {
        const docs = [];
        docs.push('# AI CLI Tool - Function Registry Documentation\n');
        docs.push('## Overview');
        docs.push(`Total registered tools: ${this.tools.size}\n`);
        // Tools by category
        docs.push('## Tools by Category\n');
        for (const [category, toolNames] of this.categories.entries()) {
            docs.push(`### ${category.charAt(0).toUpperCase() + category.slice(1)}`);
            docs.push(`Tools: ${toolNames.join(', ')}\n`);
        }
        // Individual tool documentation
        docs.push('## Tool Details\n');
        for (const tool of this.getAllTools()) {
            docs.push(tool.generateParameterDocs());
            docs.push('\n---\n');
        }
        return docs.join('\n');
    }
    // Private helper methods
    updateExecutionStats(toolName, result) {
        const stats = this.executionStats.get(toolName);
        if (stats) {
            stats.totalExecutions++;
            stats.totalExecutionTime += result.duration;
            if (result.success) {
                stats.successfulExecutions++;
            }
            else {
                stats.failedExecutions++;
            }
        }
    }
    isCriticalTool(toolName) {
        // Define critical tools that should stop execution on failure
        const criticalTools = ['file_write', 'system_command', 'database_execute'];
        return criticalTools.includes(toolName);
    }
    calculateRelevanceScore(tool, intent, context) {
        let score = 0;
        // Name match
        if (tool.name.toLowerCase().includes(intent)) {
            score += 10;
        }
        // Description match
        if (tool.description.toLowerCase().includes(intent)) {
            score += 5;
        }
        // Context-based scoring
        if (intent.includes('file') && tool.name.includes('file')) {
            score += 3;
        }
        if (intent.includes('web') && tool.name.includes('web')) {
            score += 3;
        }
        return score;
    }
    // Clear all tools (useful for testing)
    clear() {
        this.tools.clear();
        this.categories.clear();
        this.executionStats.clear();
        logger.info('Function registry cleared');
    }
}
export const functionRegistry = new FunctionRegistry();
export default functionRegistry;
//# sourceMappingURL=function-registry.js.map