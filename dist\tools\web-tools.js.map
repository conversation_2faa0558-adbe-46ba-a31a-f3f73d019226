{"version": 3, "file": "web-tools.js", "sourceRoot": "", "sources": ["../../src/tools/web-tools.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAA6C,eAAe,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAErH,iBAAiB;AACjB,MAAM,OAAO,YAAa,SAAQ,QAAQ;IAC/B,IAAI,GAAG,WAAW,CAAC;IACnB,WAAW,GAAG,oEAAoE,CAAC;IACnF,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,GAAG,EAAE,eAAe,CAAC,QAAQ,EAAE,2BAA2B,CAAC;YAC3D,MAAM,EAAE,eAAe,CAAC,QAAQ,EAAE,oBAAoB,EAAE,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;YACnG,OAAO,EAAE,eAAe,CAAC,QAAQ,EAAE,oCAAoC,EAAE;gBACvE,UAAU,EAAE,EAAE;aACf,CAAC;YACF,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,+CAA+C,CAAC;YAChF,OAAO,EAAE,eAAe,CAAC,QAAQ,EAAE,kDAAkD,CAAC;YACtF,eAAe,EAAE,eAAe,CAAC,SAAS,EAAE,uCAAuC,CAAC;YACpF,gBAAgB,EAAE,eAAe,CAAC,QAAQ,EAAE,iDAAiD,CAAC;SAC/F;QACD,QAAQ,EAAE,CAAC,KAAK,CAAC;KAClB,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,uBAAuB,EACvB,EAAE,GAAG,EAAE,qBAAqB,EAAE,EAC9B,qCAAqC,CACtC;QACD,aAAa,CACX,6BAA6B,EAC7B;YACE,GAAG,EAAE,8BAA8B;YACnC,OAAO,EAAE,EAAE,eAAe,EAAE,iBAAiB,EAAE;SAChD,EACD,gCAAgC,CACjC;QACD,aAAa,CACX,wBAAwB,EACxB;YACE,GAAG,EAAE,gCAAgC;YACrC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,IAAI,EAAE,kBAAkB;SACzB,EACD,mCAAmC,CACpC;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;YACrB,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;YAC/D,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;YACxC,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;YAC3B,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAC7C,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAC1C,gBAAgB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO;SACnE,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,UAAU,CAAC;YAE9F,4CAA4C;YAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,iBAAiB,CAC3B,6CAA6C,EAC7C,EAAE,GAAG,EAAE,EACP,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG;gBACb,MAAM;gBACN,GAAG;gBACH,OAAO,EAAE;oBACP,YAAY,EAAE,iBAAiB;oBAC/B,GAAG,OAAO;iBACX;gBACD,OAAO;gBACP,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,gBAAgB;gBAChB,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,mCAAmC;gBAC/D,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;aAC5B,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC;YAErC,yBAAyB;YACzB,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YAC3D,IAAI,gBAAgB,GAAG,QAAQ,CAAC,IAAI,CAAC;YAErC,kCAAkC;YAClC,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC7C,gBAAgB,GAAG,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ;oBAClD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAC3B,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;YACpB,CAAC;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACzC,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC;gBAC9B,OAAO,EAAE,gBAAgB;gBACzB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,WAAW;gBACX,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM;aACnC,EAAE;gBACD,GAAG;gBACH,MAAM;gBACN,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACjC,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,YAAY,GAAG,oBAAoB,CAAC;YAExC,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC/B,YAAY,GAAG,mCAAmC,CAAC;YACrD,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBACzC,YAAY,GAAG,8BAA8B,CAAC;YAChD,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBACtC,YAAY,GAAG,iBAAiB,CAAC;YACnC,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAC1B,YAAY,GAAG,QAAQ,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC/E,CAAC;YAED,OAAO,IAAI,CAAC,iBAAiB,CAC3B,GAAG,YAAY,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAC5E;gBACE,GAAG,EAAE,UAAU,CAAC,GAAG;gBACnB,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;aACnC,EACD,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,UAAU,CAAC,GAAW;QAC5B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAE/C,kDAAkD;YAClD,MAAM,eAAe,GAAG;gBACtB,aAAa;gBACb,QAAQ;gBACR,OAAO;gBACP,+BAA+B;gBAC/B,aAAa;gBACb,aAAa,EAAE,aAAa;gBAC5B,OAAO,EAAE,iBAAiB;gBAC1B,QAAQ,EAAE,eAAe;gBACzB,QAAQ,EAAE,kBAAkB;aAC7B,CAAC;YAEF,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjE,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC,CAAC,qBAAqB;QACpC,CAAC;IACH,CAAC;CACF;AAED,6DAA6D;AAC7D,MAAM,OAAO,aAAc,SAAQ,QAAQ;IAChC,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,qDAAqD,CAAC;IACpE,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,KAAK,EAAE,eAAe,CAAC,QAAQ,EAAE,yBAAyB,CAAC;YAC3D,UAAU,EAAE,eAAe,CAAC,QAAQ,EAAE,kDAAkD,CAAC;YACzF,QAAQ,EAAE,eAAe,CAAC,QAAQ,EAAE,2CAA2C,CAAC;YAChF,MAAM,EAAE,eAAe,CAAC,QAAQ,EAAE,sDAAsD,CAAC;YACzF,UAAU,EAAE,eAAe,CAAC,SAAS,EAAE,8CAA8C,CAAC;SACvF;QACD,QAAQ,EAAE,CAAC,OAAO,CAAC;KACpB,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,kBAAkB,EAClB,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAChD,4CAA4C,CAC7C;QACD,aAAa,CACX,wBAAwB,EACxB,EAAE,KAAK,EAAE,2BAA2B,EAAE,UAAU,EAAE,CAAC,EAAE,EACrD,kDAAkD,CACnD;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;YAChD,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;YAC7B,UAAU,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;SACtC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,UAAU,CAAC;YAEvE,yEAAyE;YACzE,gFAAgF;YAChF,uCAAuC;YACvC,0CAA0C;YAE1C,6CAA6C;YAC7C,MAAM,gBAAgB,GAAG;gBACvB;oBACE,KAAK,EAAE,uBAAuB,KAAK,EAAE;oBACrC,GAAG,EAAE,qCAAqC;oBAC1C,OAAO,EAAE,oDAAoD,KAAK,iGAAiG;oBACnK,UAAU,EAAE,aAAa;oBACzB,IAAI,EAAE,CAAC;iBACR;gBACD;oBACE,KAAK,EAAE,wBAAwB,KAAK,EAAE;oBACtC,GAAG,EAAE,qCAAqC;oBAC1C,OAAO,EAAE,sCAAsC,KAAK,4FAA4F;oBAChJ,UAAU,EAAE,aAAa;oBACzB,IAAI,EAAE,CAAC;iBACR;aACF,CAAC;YAEF,MAAM,OAAO,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAEtD,OAAO,IAAI,CAAC,mBAAmB,CAAC;gBAC9B,KAAK;gBACL,OAAO;gBACP,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,gBAAgB,EAAE;oBAChB,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,UAAU;iBACX;gBACD,IAAI,EAAE,kFAAkF;aACzF,EAAE;gBACD,KAAK;gBACL,WAAW,EAAE,OAAO,CAAC,MAAM;aAC5B,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,kBAAkB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAC1E,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,EAC3B,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAED,oBAAoB;AACpB,MAAM,OAAO,eAAgB,SAAQ,QAAQ;IAClC,IAAI,GAAG,aAAa,CAAC;IACrB,WAAW,GAAG,2CAA2C,CAAC;IAC1D,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,GAAG,EAAE,eAAe,CAAC,QAAQ,EAAE,gBAAgB,CAAC;YAChD,WAAW,EAAE,eAAe,CAAC,SAAS,EAAE,4CAA4C,CAAC;YACrF,eAAe,EAAE,eAAe,CAAC,SAAS,EAAE,iDAAiD,CAAC;YAC9F,eAAe,EAAE,eAAe,CAAC,SAAS,EAAE,+CAA+C,CAAC;SAC7F;QACD,QAAQ,EAAE,CAAC,KAAK,CAAC;KAClB,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,oBAAoB,EACpB,EAAE,GAAG,EAAE,sCAAsC,EAAE,EAC/C,iDAAiD,CAClD;QACD,aAAa,CACX,6BAA6B,EAC7B,EAAE,GAAG,EAAE,qBAAqB,EAAE,eAAe,EAAE,IAAI,EAAE,EACrD,oEAAoE,CACrE;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;YACrB,WAAW,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YACtC,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAC3C,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;SAC3C,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,UAAU,CAAC;YAE1E,uBAAuB;YACvB,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,MAAM,QAAQ,GAAG;gBACf,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;gBAClE,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,YAAY,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;aAChE,CAAC;YAEF,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,IAAI,QAAQ,GAAG,IAAI,CAAC;YACpB,IAAI,QAAQ,GAAG,GAAG,CAAC;YAEnB,IAAI,WAAW,EAAE,CAAC;gBAChB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;wBACrC,OAAO,EAAE,KAAK;wBACd,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI;qBAC3B,CAAC,CAAC;oBAEH,UAAU,GAAG;wBACX,MAAM,EAAE,QAAQ,CAAC,MAAM;wBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;wBAC/B,UAAU,EAAE,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG;wBAC3D,OAAO,EAAE,QAAQ,CAAC,OAAO;qBAC1B,CAAC;oBAEF,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,IAAI,GAAG,CAAC;gBACtD,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,UAAU,GAAG;wBACX,MAAM,EAAE,IAAI;wBACZ,UAAU,EAAE,KAAK,CAAC,OAAO;wBACzB,UAAU,EAAE,KAAK;wBACjB,KAAK,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;qBACrC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,eAAe,IAAI,UAAU,EAAE,UAAU,EAAE,CAAC;gBAC9C,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;wBACzC,OAAO,EAAE,KAAK;wBACd,gBAAgB,EAAE,OAAO,EAAE,oCAAoC;qBAChE,CAAC,CAAC;oBAEH,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;oBAC3B,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC5C,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,QAAQ,GAAG,EAAE,KAAK,EAAE,4BAA4B,EAAE,CAAC;gBACrD,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC;gBAC9B,QAAQ;gBACR,QAAQ,EAAE,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;gBACjD,MAAM,EAAE,UAAU;gBAClB,QAAQ;gBACR,QAAQ,EAAE;oBACR,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,QAAQ;oBACrC,WAAW,EAAE,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,MAAM,CAAC,QAAQ,KAAK,WAAW;oBAC/E,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;iBAC/C;aACF,EAAE;gBACD,GAAG;gBACH,UAAU,EAAE,UAAU,EAAE,UAAU;aACnC,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAChF,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,EACvB,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,IAAY;QACtC,MAAM,QAAQ,GAAQ,EAAE,CAAC;QAEzB,gBAAgB;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAC/D,IAAI,UAAU,EAAE,CAAC;YACf,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACxC,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACnD,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC1D,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAClE,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAEhE,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,GAAG,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjD,IAAI,GAAG,EAAE,CAAC;oBACR,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,WAAW,CAAC,QAAgB;QAClC,MAAM,aAAa,GAAG;YACpB,OAAO;YACP,+BAA+B;YAC/B,aAAa;SACd,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3D,CAAC;CACF"}