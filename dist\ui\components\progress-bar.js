import chalk from 'chalk';
export class ProgressBar {
    options;
    startTime;
    lastUpdate;
    isComplete = false;
    current = 0;
    total = 100;
    message = '';
    constructor(options = {}) {
        this.options = {
            width: 40,
            complete: '█',
            incomplete: '░',
            showPercentage: true,
            showValue: true,
            showEta: true,
            theme: 'default',
            color: 'cyan',
            ...options,
        };
        this.startTime = new Date();
        this.lastUpdate = new Date();
    }
    // Update progress
    update(update) {
        this.current = Math.max(0, Math.min(update.total, update.current));
        this.total = Math.max(1, update.total);
        this.message = update.message || this.message;
        this.lastUpdate = new Date();
        this.isComplete = this.current >= this.total;
        this.render();
    }
    // Set progress to completion
    complete(finalMessage) {
        this.current = this.total;
        this.isComplete = true;
        this.message = finalMessage || this.message;
        this.render();
        console.log(); // New line after completion
    }
    // Increment progress by a specific amount
    increment(amount = 1, message) {
        this.update({
            current: this.current + amount,
            total: this.total,
            message,
        });
    }
    // Set total and optionally reset current
    setTotal(total, resetCurrent = false) {
        this.total = Math.max(1, total);
        if (resetCurrent) {
            this.current = 0;
        }
        this.render();
    }
    // Get current progress percentage
    getPercentage() {
        return Math.round((this.current / this.total) * 100);
    }
    // Get elapsed time in milliseconds
    getElapsedTime() {
        return this.lastUpdate.getTime() - this.startTime.getTime();
    }
    // Get estimated time remaining in milliseconds
    getETA() {
        if (this.current === 0)
            return 0;
        const elapsed = this.getElapsedTime();
        const rate = this.current / elapsed;
        const remaining = this.total - this.current;
        return remaining / rate;
    }
    // Format time duration
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        }
        else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        }
        else {
            return `${seconds}s`;
        }
    }
    // Render the progress bar
    render() {
        const percentage = this.getPercentage();
        const bar = this.createBar(percentage);
        let output = '';
        switch (this.options.theme) {
            case 'minimal':
                output = this.renderMinimal(bar, percentage);
                break;
            case 'detailed':
                output = this.renderDetailed(bar, percentage);
                break;
            default:
                output = this.renderDefault(bar, percentage);
        }
        // Clear line and write new content
        process.stdout.write('\r\x1B[K' + output);
    }
    // Create the visual bar
    createBar(percentage) {
        const completeLength = Math.round((this.options.width * percentage) / 100);
        const incompleteLength = this.options.width - completeLength;
        const completeBar = this.options.complete.repeat(completeLength);
        const incompleteBar = this.options.incomplete.repeat(incompleteLength);
        return chalk[this.options.color](completeBar) +
            chalk.gray(incompleteBar);
    }
    // Render minimal theme
    renderMinimal(bar, percentage) {
        let output = bar;
        if (this.options.showPercentage) {
            output += ` ${percentage}%`;
        }
        return output;
    }
    // Render default theme
    renderDefault(bar, percentage) {
        let output = `[${bar}]`;
        if (this.options.showPercentage) {
            output += ` ${percentage}%`;
        }
        if (this.options.showValue) {
            output += ` (${this.current}/${this.total})`;
        }
        if (this.message) {
            output += ` ${this.message}`;
        }
        return output;
    }
    // Render detailed theme
    renderDetailed(bar, percentage) {
        let output = `[${bar}]`;
        if (this.options.showPercentage) {
            output += ` ${percentage}%`;
        }
        if (this.options.showValue) {
            output += ` (${this.current}/${this.total})`;
        }
        // Add timing information
        const elapsed = this.formatTime(this.getElapsedTime());
        output += ` | Elapsed: ${elapsed}`;
        if (this.options.showEta && !this.isComplete && this.current > 0) {
            const eta = this.formatTime(this.getETA());
            output += ` | ETA: ${eta}`;
        }
        if (this.message) {
            output += ` | ${this.message}`;
        }
        return output;
    }
    // Static method to create and run a simple progress bar
    static async simulate(duration, steps = 100, message = 'Processing...', options = {}) {
        return new Promise((resolve) => {
            const progressBar = new ProgressBar(options);
            const stepDuration = duration / steps;
            let currentStep = 0;
            const interval = setInterval(() => {
                currentStep++;
                progressBar.update({
                    current: currentStep,
                    total: steps,
                    message: `${message} (${currentStep}/${steps})`,
                });
                if (currentStep >= steps) {
                    clearInterval(interval);
                    progressBar.complete('Completed!');
                    resolve();
                }
            }, stepDuration);
        });
    }
}
// Multi-progress bar manager for handling multiple concurrent progress bars
export class MultiProgressBar {
    progressBars = new Map();
    isRendering = false;
    renderInterval = null;
    // Add a new progress bar
    addProgressBar(id, options = {}) {
        const progressBar = new ProgressBar(options);
        this.progressBars.set(id, progressBar);
        this.startRendering();
        return progressBar;
    }
    // Update a specific progress bar
    updateProgressBar(id, update) {
        const progressBar = this.progressBars.get(id);
        if (progressBar) {
            progressBar.update(update);
        }
    }
    // Complete a specific progress bar
    completeProgressBar(id, finalMessage) {
        const progressBar = this.progressBars.get(id);
        if (progressBar) {
            progressBar.complete(finalMessage);
        }
    }
    // Remove a progress bar
    removeProgressBar(id) {
        this.progressBars.delete(id);
        if (this.progressBars.size === 0) {
            this.stopRendering();
        }
    }
    // Get a specific progress bar
    getProgressBar(id) {
        return this.progressBars.get(id);
    }
    // Clear all progress bars
    clear() {
        this.progressBars.clear();
        this.stopRendering();
        console.log(); // New line after clearing
    }
    // Start rendering all progress bars
    startRendering() {
        if (!this.isRendering && this.progressBars.size > 0) {
            this.isRendering = true;
            this.renderInterval = setInterval(() => {
                this.renderAll();
            }, 100); // Update every 100ms
        }
    }
    // Stop rendering
    stopRendering() {
        if (this.renderInterval) {
            clearInterval(this.renderInterval);
            this.renderInterval = null;
        }
        this.isRendering = false;
    }
    // Render all progress bars
    renderAll() {
        if (this.progressBars.size === 0)
            return;
        // Move cursor up to overwrite previous output
        const lineCount = this.progressBars.size;
        if (lineCount > 1) {
            process.stdout.write(`\x1B[${lineCount - 1}A`);
        }
        // Render each progress bar
        for (const [id, progressBar] of this.progressBars.entries()) {
            process.stdout.write(`\r\x1B[K${id}: `);
            progressBar.render(); // Access private render method
            console.log();
        }
    }
}
// Utility functions for common progress bar patterns
export function createFileProgressBar(filename) {
    return new ProgressBar({
        theme: 'detailed',
        color: 'green',
        showEta: true,
    });
}
export function createDownloadProgressBar() {
    return new ProgressBar({
        theme: 'detailed',
        color: 'blue',
        complete: '▓',
        incomplete: '░',
        showEta: true,
    });
}
export function createTaskProgressBar(taskName) {
    return new ProgressBar({
        theme: 'default',
        color: 'cyan',
        showValue: true,
        showPercentage: true,
    });
}
//# sourceMappingURL=progress-bar.js.map