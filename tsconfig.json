{"compilerOptions": {"target": "ES2022", "module": "Node16", "moduleResolution": "node16", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "allowImportingTsExtensions": false, "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}