export interface BallAnimationOptions {
    speed: number;
    color: string;
    showTimer: boolean;
    prefix?: string;
    suffix?: string;
}
export declare class BallAnimation {
    private frames;
    private currentFrame;
    private interval;
    private startTime;
    private options;
    private isRunning;
    constructor(options?: Partial<BallAnimationOptions>);
    start(message?: string): void;
    stop(finalMessage?: string): void;
    updateMessage(message: string): void;
    getElapsedTime(): number;
    getElapsedTimeFormatted(): string;
    private render;
    static animate(duration: number, message: string, options?: Partial<BallAnimationOptions>): Promise<void>;
    waitForStop(): Promise<void>;
}
export declare class AnimationManager {
    private animations;
    createAnimation(id: string, options?: Partial<BallAnimationOptions>): BallAnimation;
    startAnimation(id: string, message?: string): boolean;
    stopAnimation(id: string, finalMessage?: string): boolean;
    updateAnimation(id: string, message: string): boolean;
    stopAll(finalMessage?: string): void;
    removeAnimation(id: string): boolean;
    getAnimation(id: string): BallAnimation | undefined;
    getAllAnimations(): BallAnimation[];
    clear(): void;
}
export declare const animationManager: AnimationManager;
//# sourceMappingURL=ball-animation.d.ts.map