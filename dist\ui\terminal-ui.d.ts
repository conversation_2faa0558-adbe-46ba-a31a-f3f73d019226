export interface TerminalUIOptions {
    theme: 'dark' | 'light';
    verbosity: 'quiet' | 'normal' | 'verbose' | 'debug';
    showBranding: boolean;
    enableAnimations: boolean;
}
export declare class TerminalUI {
    private options;
    private reasoningDisplay;
    constructor(options?: Partial<TerminalUIOptions>);
    showWelcome(): void;
    showHelp(commands: Array<{
        name: string;
        description: string;
        usage?: string;
    }>): void;
    showError(message: string, details?: string): void;
    showSuccess(message: string, details?: string): void;
    showWarning(message: string, details?: string): void;
    showInfo(message: string, details?: string): void;
    showDebug(message: string, data?: any): void;
    startLoading(message: string, id?: string): string;
    updateLoading(id: string, message: string): void;
    stopLoading(id: string, finalMessage?: string): void;
    displayAIResponse(content: string, streaming?: boolean): void;
    displayReasoning(steps: any[]): void;
    startReasoningDisplay(): void;
    stopReasoningDisplay(): void;
    displayToolExecution(toolName: string, parameters: any, result: any): void;
    displayConfig(config: any): void;
    displayProgress(current: number, total: number, message?: string): void;
    clear(): void;
    updateOptions(options: Partial<TerminalUIOptions>): void;
    getOptions(): TerminalUIOptions;
}
export declare const terminalUI: TerminalUI;
export default terminalUI;
//# sourceMappingURL=terminal-ui.d.ts.map