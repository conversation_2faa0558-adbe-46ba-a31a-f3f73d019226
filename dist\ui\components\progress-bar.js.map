{"version": 3, "file": "progress-bar.js", "sourceRoot": "", "sources": ["../../../src/ui/components/progress-bar.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAoB1B,MAAM,OAAO,WAAW;IACd,OAAO,CAAqB;IAC5B,SAAS,CAAO;IAChB,UAAU,CAAO;IACjB,UAAU,GAAY,KAAK,CAAC;IAC5B,OAAO,GAAW,CAAC,CAAC;IACpB,KAAK,GAAW,GAAG,CAAC;IACpB,OAAO,GAAW,EAAE,CAAC;IAE7B,YAAY,UAAuC,EAAE;QACnD,IAAI,CAAC,OAAO,GAAG;YACb,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG;YACf,cAAc,EAAE,IAAI;YACpB,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,MAAM;YACb,GAAG,OAAO;SACX,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED,kBAAkB;IAClB,MAAM,CAAC,MAAsB;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC;QAE7C,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED,6BAA6B;IAC7B,QAAQ,CAAC,YAAqB;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,4BAA4B;IAC7C,CAAC;IAED,0CAA0C;IAC1C,SAAS,CAAC,SAAiB,CAAC,EAAE,OAAgB;QAC5C,IAAI,CAAC,MAAM,CAAC;YACV,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,MAAM;YAC9B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED,yCAAyC;IACzC,QAAQ,CAAC,KAAa,EAAE,eAAwB,KAAK;QACnD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAChC,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED,kCAAkC;IAClC,aAAa;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;IACvD,CAAC;IAED,mCAAmC;IACnC,cAAc;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC9D,CAAC;IAED,+CAA+C;IAC/C,MAAM;QACJ,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5C,OAAO,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,uBAAuB;IACf,UAAU,CAAC,YAAoB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QAEvC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,EAAE,GAAG,CAAC;QACtC,CAAC;aAAM,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,OAAO,KAAK,OAAO,GAAG,EAAE,GAAG,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,OAAO,GAAG,CAAC;QACvB,CAAC;IACH,CAAC;IAED,0BAA0B;IAClB,MAAM;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAEvC,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,KAAK,SAAS;gBACZ,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,UAAU;gBACb,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;gBAC9C,MAAM;YACR;gBACE,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,mCAAmC;QACnC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,wBAAwB;IAChB,SAAS,CAAC,UAAkB;QAClC,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;QAC3E,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,cAAc,CAAC;QAE7D,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAEvE,OAAQ,KAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC;YAC/C,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC;IAED,uBAAuB;IACf,aAAa,CAAC,GAAW,EAAE,UAAkB;QACnD,IAAI,MAAM,GAAG,GAAG,CAAC;QAEjB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,IAAI,IAAI,UAAU,GAAG,CAAC;QAC9B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,uBAAuB;IACf,aAAa,CAAC,GAAW,EAAE,UAAkB;QACnD,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC;QAExB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,IAAI,IAAI,UAAU,GAAG,CAAC;QAC9B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,wBAAwB;IAChB,cAAc,CAAC,GAAW,EAAE,UAAkB;QACpD,IAAI,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC;QAExB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,IAAI,IAAI,UAAU,GAAG,CAAC;QAC9B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC;QAC/C,CAAC;QAED,yBAAyB;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACvD,MAAM,IAAI,eAAe,OAAO,EAAE,CAAC;QAEnC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YACjE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAC3C,MAAM,IAAI,WAAW,GAAG,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,wDAAwD;IACxD,MAAM,CAAC,KAAK,CAAC,QAAQ,CACnB,QAAgB,EAChB,QAAgB,GAAG,EACnB,UAAkB,eAAe,EACjC,UAAuC,EAAE;QAEzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,YAAY,GAAG,QAAQ,GAAG,KAAK,CAAC;YACtC,IAAI,WAAW,GAAG,CAAC,CAAC;YAEpB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;gBAChC,WAAW,EAAE,CAAC;gBACd,WAAW,CAAC,MAAM,CAAC;oBACjB,OAAO,EAAE,WAAW;oBACpB,KAAK,EAAE,KAAK;oBACZ,OAAO,EAAE,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG;iBAChD,CAAC,CAAC;gBAEH,IAAI,WAAW,IAAI,KAAK,EAAE,CAAC;oBACzB,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;oBACnC,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,EAAE,YAAY,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,4EAA4E;AAC5E,MAAM,OAAO,gBAAgB;IACnB,YAAY,GAA6B,IAAI,GAAG,EAAE,CAAC;IACnD,WAAW,GAAY,KAAK,CAAC;IAC7B,cAAc,GAA0B,IAAI,CAAC;IAErD,yBAAyB;IACzB,cAAc,CACZ,EAAU,EACV,UAAuC,EAAE;QAEzC,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,iCAAiC;IACjC,iBAAiB,CAAC,EAAU,EAAE,MAAsB;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC9C,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,mCAAmC;IACnC,mBAAmB,CAAC,EAAU,EAAE,YAAqB;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC9C,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,wBAAwB;IACxB,iBAAiB,CAAC,EAAU;QAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED,8BAA8B;IAC9B,cAAc,CAAC,EAAU;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;IAED,0BAA0B;IAC1B,KAAK;QACH,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,0BAA0B;IAC3C,CAAC;IAED,oCAAoC;IAC5B,cAAc;QACpB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;gBACrC,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,qBAAqB;QAChC,CAAC;IACH,CAAC;IAED,iBAAiB;IACT,aAAa;QACnB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,2BAA2B;IACnB,SAAS;QACf,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;YAAE,OAAO;QAEzC,8CAA8C;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QACzC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;YAClB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC;QAED,2BAA2B;QAC3B,KAAK,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACvC,WAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,+BAA+B;YAC9D,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;CACF;AAED,qDAAqD;AACrD,MAAM,UAAU,qBAAqB,CAAC,QAAgB;IACpD,OAAO,IAAI,WAAW,CAAC;QACrB,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,OAAO;QACd,OAAO,EAAE,IAAI;KACd,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,yBAAyB;IACvC,OAAO,IAAI,WAAW,CAAC;QACrB,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,MAAM;QACb,QAAQ,EAAE,GAAG;QACb,UAAU,EAAE,GAAG;QACf,OAAO,EAAE,IAAI;KACd,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,QAAgB;IACpD,OAAO,IAAI,WAAW,CAAC;QACrB,KAAK,EAAE,SAAS;QAChB,KAAK,EAAE,MAAM;QACb,SAAS,EAAE,IAAI;QACf,cAAc,EAAE,IAAI;KACrB,CAAC,CAAC;AACL,CAAC"}