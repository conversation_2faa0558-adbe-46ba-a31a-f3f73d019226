import chalk from 'chalk';
import figlet from 'figlet';
import boxen from 'boxen';
import { animationManager } from './components/ball-animation.js';
import { createReasoningDisplay } from './components/reasoning-display.js';
import { settings } from '../config/settings.js';
export class TerminalUI {
    options;
    reasoningDisplay;
    constructor(options = {}) {
        this.options = {
            theme: settings.settings.ui.theme,
            verbosity: settings.settings.ui.verbosity,
            showBranding: true,
            enableAnimations: true,
            ...options,
        };
        this.reasoningDisplay = createReasoningDisplay(this.options.verbosity === 'verbose' ? 'verbose' :
            this.options.verbosity === 'quiet' ? 'minimal' : 'compact');
    }
    // Display welcome banner
    showWelcome() {
        if (this.options.verbosity === 'quiet')
            return;
        console.clear();
        if (this.options.showBranding) {
            const title = figlet.textSync('AI CLI', {
                font: 'ANSI Shadow',
                horizontalLayout: 'fitted',
                width: 80,
            });
            console.log(chalk.cyan(title));
            console.log(chalk.gray('Comprehensive AI-powered CLI tool with DeepSeek integration\n'));
        }
        const welcomeBox = boxen(chalk.white.bold('Welcome to AI CLI Tool') + '\n\n' +
            chalk.gray('• Type ') + chalk.cyan('help') + chalk.gray(' to see available commands\n') +
            chalk.gray('• Type ') + chalk.cyan('config') + chalk.gray(' to configure settings\n') +
            chalk.gray('• Type ') + chalk.cyan('exit') + chalk.gray(' to quit\n'), {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'cyan',
            align: 'left',
        });
        console.log(welcomeBox);
    }
    // Display help information
    showHelp(commands) {
        console.log(chalk.cyan.bold('\n📚 Available Commands:\n'));
        const maxNameLength = Math.max(...commands.map(cmd => cmd.name.length));
        for (const command of commands) {
            const name = chalk.green(command.name.padEnd(maxNameLength));
            const description = chalk.white(command.description);
            console.log(`  ${name}  ${description}`);
            if (command.usage && this.options.verbosity !== 'quiet') {
                console.log(chalk.gray(`    Usage: ${command.usage}`));
            }
        }
        console.log();
    }
    // Display error message
    showError(message, details) {
        const errorBox = boxen(chalk.red.bold('❌ Error') + '\n\n' +
            chalk.white(message) +
            (details ? '\n\n' + chalk.gray(details) : ''), {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'red',
            align: 'left',
        });
        console.log(errorBox);
    }
    // Display success message
    showSuccess(message, details) {
        if (this.options.verbosity === 'quiet')
            return;
        const successBox = boxen(chalk.green.bold('✅ Success') + '\n\n' +
            chalk.white(message) +
            (details ? '\n\n' + chalk.gray(details) : ''), {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'green',
            align: 'left',
        });
        console.log(successBox);
    }
    // Display warning message
    showWarning(message, details) {
        if (this.options.verbosity === 'quiet')
            return;
        const warningBox = boxen(chalk.yellow.bold('⚠️  Warning') + '\n\n' +
            chalk.white(message) +
            (details ? '\n\n' + chalk.gray(details) : ''), {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'yellow',
            align: 'left',
        });
        console.log(warningBox);
    }
    // Display info message
    showInfo(message, details) {
        if (this.options.verbosity === 'quiet')
            return;
        console.log(chalk.blue('ℹ️  ') + chalk.white(message));
        if (details && this.options.verbosity === 'verbose') {
            console.log(chalk.gray(details));
        }
    }
    // Display debug message
    showDebug(message, data) {
        if (this.options.verbosity !== 'debug')
            return;
        console.log(chalk.magenta('🐛 DEBUG: ') + chalk.white(message));
        if (data) {
            console.log(chalk.gray(JSON.stringify(data, null, 2)));
        }
    }
    // Create and start a loading animation
    startLoading(message, id) {
        if (!this.options.enableAnimations || this.options.verbosity === 'quiet') {
            console.log(chalk.blue('⏳ ') + message);
            return '';
        }
        const animationId = id || `loading_${Date.now()}`;
        const animation = animationManager.createAnimation(animationId, {
            color: 'cyan',
            showTimer: true,
            prefix: '⏳ ',
        });
        animation.start(message);
        return animationId;
    }
    // Update loading message
    updateLoading(id, message) {
        if (this.options.enableAnimations && this.options.verbosity !== 'quiet') {
            animationManager.updateAnimation(id, message);
        }
    }
    // Stop loading animation
    stopLoading(id, finalMessage) {
        if (this.options.enableAnimations && this.options.verbosity !== 'quiet') {
            animationManager.stopAnimation(id, finalMessage);
        }
        else if (finalMessage) {
            console.log(chalk.green('✅ ') + finalMessage);
        }
    }
    // Display AI response with streaming support
    displayAIResponse(content, streaming = false) {
        if (streaming) {
            process.stdout.write(chalk.white(content));
        }
        else {
            const responseBox = boxen(chalk.white(content), {
                padding: 1,
                margin: 1,
                borderStyle: 'single',
                borderColor: 'blue',
                align: 'left',
                title: '🤖 AI Response',
                titleAlignment: 'left',
            });
            console.log(responseBox);
        }
    }
    // Display reasoning steps
    displayReasoning(steps) {
        if (!settings.settings.ui.showReasoningSteps || this.options.verbosity === 'quiet') {
            return;
        }
        for (const step of steps) {
            this.reasoningDisplay.addStep(step);
        }
    }
    // Start reasoning display
    startReasoningDisplay() {
        if (settings.settings.ui.showReasoningSteps && this.options.verbosity !== 'quiet') {
            this.reasoningDisplay.startStreaming();
        }
    }
    // Stop reasoning display
    stopReasoningDisplay() {
        if (settings.settings.ui.showReasoningSteps && this.options.verbosity !== 'quiet') {
            this.reasoningDisplay.stopStreaming();
        }
    }
    // Display tool execution
    displayToolExecution(toolName, parameters, result) {
        if (this.options.verbosity === 'quiet')
            return;
        console.log(chalk.yellow('🔧 ') + chalk.white(`Executing tool: ${toolName}`));
        if (this.options.verbosity === 'verbose' || this.options.verbosity === 'debug') {
            console.log(chalk.gray('Parameters:'), parameters);
        }
        if (result.success) {
            console.log(chalk.green('✅ ') + chalk.white('Tool executed successfully'));
            if (this.options.verbosity === 'verbose' && result.result) {
                console.log(chalk.gray('Result:'), result.result);
            }
        }
        else {
            console.log(chalk.red('❌ ') + chalk.white(`Tool execution failed: ${result.error}`));
        }
    }
    // Display configuration
    displayConfig(config) {
        const configBox = boxen(chalk.white.bold('Current Configuration') + '\n\n' +
            Object.entries(config).map(([section, settings]) => {
                const sectionTitle = chalk.cyan.bold(section.toUpperCase());
                const sectionSettings = Object.entries(settings).map(([key, value]) => {
                    return `  ${chalk.green(key)}: ${chalk.white(String(value))}`;
                }).join('\n');
                return sectionTitle + '\n' + sectionSettings;
            }).join('\n\n'), {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'cyan',
            align: 'left',
        });
        console.log(configBox);
    }
    // Display progress bar
    displayProgress(current, total, message) {
        if (this.options.verbosity === 'quiet')
            return;
        const percentage = Math.round((current / total) * 100);
        const barLength = 30;
        const filledLength = Math.round((barLength * current) / total);
        const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
        const progressText = `${chalk.cyan(bar)} ${percentage}% (${current}/${total})`;
        const fullText = message ? `${message} ${progressText}` : progressText;
        process.stdout.write('\r' + fullText);
        if (current === total) {
            console.log(); // New line when complete
        }
    }
    // Clear screen
    clear() {
        console.clear();
    }
    // Update UI options
    updateOptions(options) {
        this.options = { ...this.options, ...options };
        // Update reasoning display based on new verbosity
        this.reasoningDisplay = createReasoningDisplay(this.options.verbosity === 'verbose' ? 'verbose' :
            this.options.verbosity === 'quiet' ? 'minimal' : 'compact');
    }
    // Get current options
    getOptions() {
        return { ...this.options };
    }
}
// Export a default instance
export const terminalUI = new TerminalUI();
export default terminalUI;
//# sourceMappingURL=terminal-ui.js.map