{"version": 3, "file": "ball-animation.js", "sourceRoot": "", "sources": ["../../../src/ui/components/ball-animation.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAU1B,MAAM,OAAO,aAAa;IAChB,MAAM,GAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAClD,YAAY,GAAG,CAAC,CAAC;IACjB,QAAQ,GAA0B,IAAI,CAAC;IACvC,SAAS,GAAgB,IAAI,CAAC;IAC9B,OAAO,CAAuB;IAC9B,SAAS,GAAG,KAAK,CAAC;IAE1B,YAAY,UAAyC,EAAE;QACrD,IAAI,CAAC,OAAO,GAAG;YACb,KAAK,EAAE,GAAG;YACV,KAAK,EAAE,MAAM;YACb,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAE;YACV,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAgB;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAEtB,cAAc;QACd,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAElC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACrB,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACnE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,IAAI,CAAC,YAAqB;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvB,CAAC;QAED,wCAAwC;QACxC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACjC,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC5B,CAAC;QAED,cAAc;QACd,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACpC,CAAC;IAED,aAAa,CAAC,OAAe;QAC3B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,CAAC,CAAC;QACX,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC/C,CAAC;IAED,uBAAuB;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,OAAO,GAAG,IAAI,CAAC;QAEpC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,YAAY,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,OAAgB;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7C,MAAM,YAAY,GAAI,KAAa,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,CAAW,CAAC;QAEzE,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC;QAEhD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC;QACxC,CAAC;QAED,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAE9B,mCAAmC;QACnC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED,+CAA+C;IAC/C,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,QAAgB,EAChB,OAAe,EACf,UAAyC,EAAE;QAE3C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;YAC7C,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEzB,UAAU,CAAC,GAAG,EAAE;gBACd,SAAS,CAAC,IAAI,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,QAAQ,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAED,qEAAqE;IACrE,WAAW;QACT,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,EAAE;gBACrC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;oBACpB,aAAa,CAAC,aAAa,CAAC,CAAC;oBAC7B,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,iDAAiD;AACjD,MAAM,OAAO,gBAAgB;IACnB,UAAU,GAA+B,IAAI,GAAG,EAAE,CAAC;IAE3D,eAAe,CAAC,EAAU,EAAE,UAAyC,EAAE;QACrE,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACnC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,cAAc,CAAC,EAAU,EAAE,OAAgB;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,aAAa,CAAC,EAAU,EAAE,YAAqB;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,eAAe,CAAC,EAAU,EAAE,OAAe;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CAAC,YAAqB;QAC3B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED,eAAe,CAAC,EAAU;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,YAAY,CAAC,EAAU;QACrB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC;IAED,gBAAgB;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;CACF;AAED,4CAA4C;AAC5C,MAAM,CAAC,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC"}