import axios from 'axios';
import { z } from 'zod';
import { BaseTool, createParameter, createExample } from './base-tool.js';
// Web Fetch Tool
export class WebFetchTool extends BaseTool {
    name = 'web_fetch';
    description = 'Fetch content from web URLs with support for various content types';
    parameters = {
        type: 'object',
        properties: {
            url: createParameter('string', 'URL to fetch content from'),
            method: createParameter('string', 'HTTP method to use', { enum: ['GET', 'POST', 'PUT', 'DELETE'] }),
            headers: createParameter('object', 'HTTP headers to include (optional)', {
                properties: {},
            }),
            body: createParameter('string', 'Request body for POST/PUT requests (optional)'),
            timeout: createParameter('number', 'Request timeout in milliseconds (default: 30000)'),
            followRedirects: createParameter('boolean', 'Follow HTTP redirects (default: true)'),
            maxContentLength: createParameter('number', 'Maximum content length in bytes (default: 10MB)'),
        },
        required: ['url'],
    };
    examples = [
        createExample('Fetch webpage content', { url: 'https://example.com' }, 'Returns HTML content of the webpage'),
        createExample('Fetch API data with headers', {
            url: 'https://api.example.com/data',
            headers: { 'Authorization': 'Bearer token123' }
        }, 'Returns JSON data from the API'),
        createExample('POST request with data', {
            url: 'https://api.example.com/submit',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: '{"key": "value"}'
        }, 'Submits data and returns response'),
    ];
    getParameterSchema() {
        return z.object({
            url: z.string().url(),
            method: z.enum(['GET', 'POST', 'PUT', 'DELETE']).default('GET'),
            headers: z.record(z.string()).optional(),
            body: z.string().optional(),
            timeout: z.number().positive().default(30000),
            followRedirects: z.boolean().default(true),
            maxContentLength: z.number().positive().default(10485760), // 10MB
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { url, method, headers, body, timeout, followRedirects, maxContentLength } = parameters;
            // Security check - block local/private URLs
            if (this.isLocalUrl(url)) {
                return this.createErrorResult('Access to local/private URLs is not allowed', { url }, Date.now() - startTime);
            }
            const config = {
                method,
                url,
                headers: {
                    'User-Agent': 'AI-CLI-Tool/1.0',
                    ...headers,
                },
                timeout,
                maxRedirects: followRedirects ? 5 : 0,
                maxContentLength,
                validateStatus: () => true, // Don't throw on HTTP error status
                ...(body && { data: body }),
            };
            const response = await axios(config);
            // Determine content type
            const contentType = response.headers['content-type'] || '';
            let processedContent = response.data;
            // Process different content types
            if (contentType.includes('application/json')) {
                processedContent = typeof response.data === 'string'
                    ? JSON.parse(response.data)
                    : response.data;
            }
            else if (contentType.includes('text/')) {
                processedContent = String(response.data);
            }
            return this.createSuccessResult({
                content: processedContent,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                contentType,
                size: String(response.data).length,
            }, {
                url,
                method,
                duration: Date.now() - startTime,
            }, Date.now() - startTime);
        }
        catch (error) {
            let errorMessage = 'Web request failed';
            if (error.code === 'ENOTFOUND') {
                errorMessage = 'Domain not found or network error';
            }
            else if (error.code === 'ECONNREFUSED') {
                errorMessage = 'Connection refused by server';
            }
            else if (error.code === 'ETIMEDOUT') {
                errorMessage = 'Request timeout';
            }
            else if (error.response) {
                errorMessage = `HTTP ${error.response.status}: ${error.response.statusText}`;
            }
            return this.createErrorResult(`${errorMessage}: ${error instanceof Error ? error.message : String(error)}`, {
                url: parameters.url,
                statusCode: error.response?.status,
            }, Date.now() - startTime);
        }
    }
    isLocalUrl(url) {
        try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname.toLowerCase();
            // Block localhost, private IPs, and local domains
            const blockedPatterns = [
                /^localhost$/,
                /^127\./,
                /^10\./,
                /^172\.(1[6-9]|2[0-9]|3[01])\./,
                /^192\.168\./,
                /^169\.254\./, // Link-local
                /^::1$/, // IPv6 localhost
                /^fc00:/, // IPv6 private
                /^fe80:/, // IPv6 link-local
            ];
            return blockedPatterns.some(pattern => pattern.test(hostname));
        }
        catch {
            return true; // Block invalid URLs
        }
    }
}
// Web Search Tool (simulated - would need actual search API)
export class WebSearchTool extends BaseTool {
    name = 'web_search';
    description = 'Search the web for information using search queries';
    parameters = {
        type: 'object',
        properties: {
            query: createParameter('string', 'Search query to execute'),
            maxResults: createParameter('number', 'Maximum number of results to return (default: 5)'),
            language: createParameter('string', 'Language for search results (default: en)'),
            region: createParameter('string', 'Region/country code for localized results (optional)'),
            safeSearch: createParameter('boolean', 'Enable safe search filtering (default: true)'),
        },
        required: ['query'],
    };
    examples = [
        createExample('Basic web search', { query: 'artificial intelligence trends 2024' }, 'Returns top search results about AI trends'),
        createExample('Limited results search', { query: 'TypeScript best practices', maxResults: 3 }, 'Returns top 3 results about TypeScript practices'),
    ];
    getParameterSchema() {
        return z.object({
            query: z.string().min(1),
            maxResults: z.number().min(1).max(20).default(5),
            language: z.string().default('en'),
            region: z.string().optional(),
            safeSearch: z.boolean().default(true),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { query, maxResults, language, region, safeSearch } = parameters;
            // Note: This is a simulated search. In a real implementation, you would:
            // 1. Use a search API like Google Custom Search, Bing Search API, or DuckDuckGo
            // 2. Handle API keys and rate limiting
            // 3. Parse and format real search results
            // Simulated search results for demonstration
            const simulatedResults = [
                {
                    title: `Search Results for: ${query}`,
                    url: 'https://example.com/search-result-1',
                    snippet: `This is a simulated search result for the query "${query}". In a real implementation, this would contain actual search results from a search engine API.`,
                    displayUrl: 'example.com',
                    rank: 1,
                },
                {
                    title: `Related Information: ${query}`,
                    url: 'https://example.com/search-result-2',
                    snippet: `Additional information related to "${query}". Real search results would provide relevant, up-to-date information from across the web.`,
                    displayUrl: 'example.com',
                    rank: 2,
                },
            ];
            const results = simulatedResults.slice(0, maxResults);
            return this.createSuccessResult({
                query,
                results,
                totalResults: results.length,
                searchParameters: {
                    language,
                    region,
                    safeSearch,
                    maxResults,
                },
                note: 'This is a simulated search. Integrate with a real search API for production use.',
            }, {
                query,
                resultCount: results.length,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`Search failed: ${error instanceof Error ? error.message : String(error)}`, { query: parameters.query }, Date.now() - startTime);
        }
    }
}
// URL Analysis Tool
export class UrlAnalysisTool extends BaseTool {
    name = 'url_analyze';
    description = 'Analyze and extract information from URLs';
    parameters = {
        type: 'object',
        properties: {
            url: createParameter('string', 'URL to analyze'),
            checkStatus: createParameter('boolean', 'Check if URL is accessible (default: true)'),
            extractMetadata: createParameter('boolean', 'Extract metadata from the page (default: false)'),
            followRedirects: createParameter('boolean', 'Follow redirects to final URL (default: true)'),
        },
        required: ['url'],
    };
    examples = [
        createExample('Basic URL analysis', { url: 'https://example.com/path?param=value' }, 'Returns URL components and accessibility status'),
        createExample('Full analysis with metadata', { url: 'https://example.com', extractMetadata: true }, 'Returns URL analysis plus page metadata like title and description'),
    ];
    getParameterSchema() {
        return z.object({
            url: z.string().url(),
            checkStatus: z.boolean().default(true),
            extractMetadata: z.boolean().default(false),
            followRedirects: z.boolean().default(true),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { url, checkStatus, extractMetadata, followRedirects } = parameters;
            // Parse URL components
            const urlObj = new URL(url);
            const analysis = {
                original: url,
                protocol: urlObj.protocol,
                hostname: urlObj.hostname,
                port: urlObj.port || (urlObj.protocol === 'https:' ? '443' : '80'),
                pathname: urlObj.pathname,
                search: urlObj.search,
                hash: urlObj.hash,
                origin: urlObj.origin,
                searchParams: Object.fromEntries(urlObj.searchParams.entries()),
            };
            let statusInfo = null;
            let metadata = null;
            let finalUrl = url;
            if (checkStatus) {
                try {
                    const response = await axios.head(url, {
                        timeout: 10000,
                        maxRedirects: followRedirects ? 5 : 0,
                        validateStatus: () => true,
                    });
                    statusInfo = {
                        status: response.status,
                        statusText: response.statusText,
                        accessible: response.status >= 200 && response.status < 400,
                        headers: response.headers,
                    };
                    finalUrl = response.request.res?.responseUrl || url;
                }
                catch (error) {
                    statusInfo = {
                        status: null,
                        statusText: error.message,
                        accessible: false,
                        error: error.code || 'UNKNOWN_ERROR',
                    };
                }
            }
            if (extractMetadata && statusInfo?.accessible) {
                try {
                    const response = await axios.get(finalUrl, {
                        timeout: 15000,
                        maxContentLength: 1048576, // 1MB limit for metadata extraction
                    });
                    const html = response.data;
                    metadata = this.extractHtmlMetadata(html);
                }
                catch (error) {
                    metadata = { error: 'Failed to extract metadata' };
                }
            }
            return this.createSuccessResult({
                analysis,
                finalUrl: finalUrl !== url ? finalUrl : undefined,
                status: statusInfo,
                metadata,
                security: {
                    isHttps: urlObj.protocol === 'https:',
                    isLocalhost: urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1',
                    isPrivateIp: this.isPrivateIp(urlObj.hostname),
                },
            }, {
                url,
                accessible: statusInfo?.accessible,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`URL analysis failed: ${error instanceof Error ? error.message : String(error)}`, { url: parameters.url }, Date.now() - startTime);
        }
    }
    extractHtmlMetadata(html) {
        const metadata = {};
        // Extract title
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        if (titleMatch) {
            metadata.title = titleMatch[1].trim();
        }
        // Extract meta tags
        const metaMatches = html.matchAll(/<meta[^>]+>/gi);
        for (const match of metaMatches) {
            const metaTag = match[0];
            const nameMatch = metaTag.match(/name=["']([^"']+)["']/i);
            const propertyMatch = metaTag.match(/property=["']([^"']+)["']/i);
            const contentMatch = metaTag.match(/content=["']([^"']+)["']/i);
            if (contentMatch) {
                const key = nameMatch?.[1] || propertyMatch?.[1];
                if (key) {
                    metadata[key] = contentMatch[1];
                }
            }
        }
        return metadata;
    }
    isPrivateIp(hostname) {
        const privateRanges = [
            /^10\./,
            /^172\.(1[6-9]|2[0-9]|3[01])\./,
            /^192\.168\./,
        ];
        return privateRanges.some(range => range.test(hostname));
    }
}
//# sourceMappingURL=web-tools.js.map