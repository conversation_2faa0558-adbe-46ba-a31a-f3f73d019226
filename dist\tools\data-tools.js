import { z } from 'zod';
import { BaseTool, createParameter, createExample } from './base-tool.js';
// JSON Processing Tool
export class JsonProcessorTool extends BaseTool {
    name = 'json_processor';
    description = 'Process, validate, and manipulate JSON data with various operations';
    parameters = {
        type: 'object',
        properties: {
            operation: createParameter('string', 'Operation to perform on JSON data', {
                enum: ['validate', 'format', 'minify', 'extract', 'merge', 'transform'],
            }),
            data: createParameter('string', 'JSON data to process (as string)'),
            path: createParameter('string', 'JSONPath expression for extract operation (optional)'),
            mergeWith: createParameter('string', 'Additional JSON data to merge with (optional)'),
            transformRules: createParameter('object', 'Transformation rules for transform operation (optional)', {
                properties: {},
            }),
            validateSchema: createParameter('object', 'JSON schema for validation (optional)', {
                properties: {},
            }),
        },
        required: ['operation', 'data'],
    };
    examples = [
        createExample('Validate JSON data', { operation: 'validate', data: '{"name": "<PERSON>", "age": 30}' }, 'Returns validation result and any errors found'),
        createExample('Format JSON with indentation', { operation: 'format', data: '{"name":"John","age":30}' }, 'Returns formatted JSON with proper indentation'),
        createExample('Extract data using JSONPath', { operation: 'extract', data: '{"users": [{"name": "John"}, {"name": "Jane"}]}', path: '$.users[*].name' }, 'Returns extracted values: ["John", "Jane"]'),
    ];
    getParameterSchema() {
        return z.object({
            operation: z.enum(['validate', 'format', 'minify', 'extract', 'merge', 'transform']),
            data: z.string(),
            path: z.string().optional(),
            mergeWith: z.string().optional(),
            transformRules: z.record(z.any()).optional(),
            validateSchema: z.record(z.any()).optional(),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { operation, data, path, mergeWith, transformRules, validateSchema } = parameters;
            // Parse input JSON
            let jsonData;
            try {
                jsonData = JSON.parse(data);
            }
            catch (error) {
                return this.createErrorResult(`Invalid JSON data: ${error instanceof Error ? error.message : String(error)}`, { operation }, Date.now() - startTime);
            }
            let result;
            switch (operation) {
                case 'validate':
                    result = this.validateJson(jsonData, validateSchema);
                    break;
                case 'format':
                    result = {
                        formatted: JSON.stringify(jsonData, null, 2),
                        size: JSON.stringify(jsonData, null, 2).length,
                    };
                    break;
                case 'minify':
                    result = {
                        minified: JSON.stringify(jsonData),
                        size: JSON.stringify(jsonData).length,
                        originalSize: data.length,
                        compressionRatio: Math.round((1 - JSON.stringify(jsonData).length / data.length) * 100),
                    };
                    break;
                case 'extract':
                    if (!path) {
                        return this.createErrorResult('JSONPath is required for extract operation', { operation }, Date.now() - startTime);
                    }
                    result = this.extractJsonPath(jsonData, path);
                    break;
                case 'merge':
                    if (!mergeWith) {
                        return this.createErrorResult('mergeWith data is required for merge operation', { operation }, Date.now() - startTime);
                    }
                    try {
                        const mergeData = JSON.parse(mergeWith);
                        result = {
                            merged: this.mergeJson(jsonData, mergeData),
                        };
                    }
                    catch (error) {
                        return this.createErrorResult(`Invalid merge JSON data: ${error instanceof Error ? error.message : String(error)}`, { operation }, Date.now() - startTime);
                    }
                    break;
                case 'transform':
                    result = {
                        transformed: this.transformJson(jsonData, transformRules || {}),
                    };
                    break;
                default:
                    return this.createErrorResult(`Unknown operation: ${operation}`, { operation }, Date.now() - startTime);
            }
            return this.createSuccessResult(result, {
                operation,
                inputSize: data.length,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`JSON processing failed: ${error instanceof Error ? error.message : String(error)}`, { operation: parameters.operation }, Date.now() - startTime);
        }
    }
    validateJson(data, schema) {
        const result = {
            valid: true,
            errors: [],
            type: this.getJsonType(data),
            structure: this.analyzeJsonStructure(data),
        };
        // Basic validation
        if (data === null) {
            result.errors.push('Data is null');
            result.valid = false;
        }
        // Schema validation (simplified - would use a proper JSON schema validator in production)
        if (schema) {
            const schemaErrors = this.validateAgainstSchema(data, schema);
            result.errors.push(...schemaErrors);
            result.valid = schemaErrors.length === 0;
        }
        return result;
    }
    getJsonType(data) {
        if (data === null)
            return 'null';
        if (Array.isArray(data))
            return 'array';
        return typeof data;
    }
    analyzeJsonStructure(data, depth = 0) {
        if (depth > 5)
            return { type: 'deep', maxDepthReached: true };
        if (Array.isArray(data)) {
            return {
                type: 'array',
                length: data.length,
                elementTypes: [...new Set(data.map(item => this.getJsonType(item)))],
                sample: data.slice(0, 3),
            };
        }
        else if (typeof data === 'object' && data !== null) {
            return {
                type: 'object',
                keys: Object.keys(data),
                keyCount: Object.keys(data).length,
                structure: Object.fromEntries(Object.entries(data).slice(0, 10).map(([key, value]) => [
                    key,
                    this.analyzeJsonStructure(value, depth + 1),
                ])),
            };
        }
        else {
            return {
                type: this.getJsonType(data),
                value: data,
            };
        }
    }
    validateAgainstSchema(data, schema) {
        const errors = [];
        // Simplified schema validation - would use ajv or similar in production
        if (schema.type && this.getJsonType(data) !== schema.type) {
            errors.push(`Expected type ${schema.type}, got ${this.getJsonType(data)}`);
        }
        if (schema.required && Array.isArray(schema.required)) {
            for (const requiredField of schema.required) {
                if (typeof data === 'object' && data !== null && !(requiredField in data)) {
                    errors.push(`Missing required field: ${requiredField}`);
                }
            }
        }
        return errors;
    }
    extractJsonPath(data, path) {
        // Simplified JSONPath implementation - would use a proper library in production
        try {
            if (path === '$')
                return data;
            // Handle simple paths like $.users[*].name
            const parts = path.replace(/^\$\.?/, '').split('.');
            let current = data;
            for (const part of parts) {
                if (part.includes('[') && part.includes(']')) {
                    const [key, indexPart] = part.split('[');
                    const index = indexPart.replace(']', '');
                    if (key)
                        current = current[key];
                    if (index === '*') {
                        if (Array.isArray(current)) {
                            return current;
                        }
                        else {
                            return Object.values(current);
                        }
                    }
                    else {
                        current = current[parseInt(index)];
                    }
                }
                else {
                    current = current[part];
                }
                if (current === undefined)
                    break;
            }
            return current;
        }
        catch (error) {
            throw new Error(`JSONPath extraction failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    mergeJson(target, source) {
        if (Array.isArray(target) && Array.isArray(source)) {
            return [...target, ...source];
        }
        if (typeof target === 'object' && target !== null && typeof source === 'object' && source !== null) {
            const result = { ...target };
            for (const [key, value] of Object.entries(source)) {
                if (key in result && typeof result[key] === 'object' && typeof value === 'object') {
                    result[key] = this.mergeJson(result[key], value);
                }
                else {
                    result[key] = value;
                }
            }
            return result;
        }
        return source; // Source overwrites target for primitive values
    }
    transformJson(data, rules) {
        // Simplified transformation - would implement more complex rules in production
        if (Array.isArray(data)) {
            return data.map(item => this.transformJson(item, rules));
        }
        if (typeof data === 'object' && data !== null) {
            const result = {};
            for (const [key, value] of Object.entries(data)) {
                const newKey = rules[key] || key;
                result[newKey] = this.transformJson(value, rules);
            }
            return result;
        }
        return data;
    }
}
// CSV Processing Tool
export class CsvProcessorTool extends BaseTool {
    name = 'csv_processor';
    description = 'Process and manipulate CSV data with various operations';
    parameters = {
        type: 'object',
        properties: {
            operation: createParameter('string', 'Operation to perform on CSV data', {
                enum: ['parse', 'generate', 'filter', 'sort', 'aggregate', 'validate'],
            }),
            data: createParameter('string', 'CSV data to process'),
            delimiter: createParameter('string', 'CSV delimiter (default: comma)'),
            hasHeader: createParameter('boolean', 'Whether CSV has header row (default: true)'),
            filterColumn: createParameter('string', 'Column name for filter operation (optional)'),
            filterValue: createParameter('string', 'Value to filter by (optional)'),
            sortColumn: createParameter('string', 'Column name for sort operation (optional)'),
            sortOrder: createParameter('string', 'Sort order: asc or desc (default: asc)', { enum: ['asc', 'desc'] }),
            aggregateColumn: createParameter('string', 'Column name for aggregation (optional)'),
            aggregateFunction: createParameter('string', 'Aggregation function', { enum: ['sum', 'avg', 'count', 'min', 'max'] }),
        },
        required: ['operation', 'data'],
    };
    examples = [
        createExample('Parse CSV data', { operation: 'parse', data: 'name,age,city\nJohn,30,NYC\nJane,25,LA', hasHeader: true }, 'Returns parsed CSV as array of objects'),
        createExample('Filter CSV data', {
            operation: 'filter',
            data: 'name,age,city\nJohn,30,NYC\nJane,25,LA',
            filterColumn: 'age',
            filterValue: '30'
        }, 'Returns filtered CSV data'),
    ];
    getParameterSchema() {
        return z.object({
            operation: z.enum(['parse', 'generate', 'filter', 'sort', 'aggregate', 'validate']),
            data: z.string(),
            delimiter: z.string().default(','),
            hasHeader: z.boolean().default(true),
            filterColumn: z.string().optional(),
            filterValue: z.string().optional(),
            sortColumn: z.string().optional(),
            sortOrder: z.enum(['asc', 'desc']).default('asc'),
            aggregateColumn: z.string().optional(),
            aggregateFunction: z.enum(['sum', 'avg', 'count', 'min', 'max']).optional(),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { operation, data, delimiter, hasHeader } = parameters;
            // Parse CSV data
            const rows = this.parseCSV(data, delimiter);
            if (rows.length === 0) {
                return this.createErrorResult('No data found in CSV', { operation }, Date.now() - startTime);
            }
            let headers = [];
            let dataRows = rows;
            if (hasHeader) {
                headers = rows[0];
                dataRows = rows.slice(1);
            }
            else {
                headers = rows[0].map((_, index) => `column_${index + 1}`);
            }
            let result;
            switch (operation) {
                case 'parse':
                    result = {
                        headers,
                        rows: dataRows,
                        objects: dataRows.map(row => Object.fromEntries(headers.map((header, index) => [header, row[index] || '']))),
                        rowCount: dataRows.length,
                        columnCount: headers.length,
                    };
                    break;
                case 'validate':
                    result = this.validateCSV(headers, dataRows);
                    break;
                case 'filter':
                    result = this.filterCSV(headers, dataRows, parameters);
                    break;
                case 'sort':
                    result = this.sortCSV(headers, dataRows, parameters);
                    break;
                case 'aggregate':
                    result = this.aggregateCSV(headers, dataRows, parameters);
                    break;
                case 'generate':
                    result = {
                        csv: this.generateCSV(headers, dataRows, delimiter),
                    };
                    break;
                default:
                    return this.createErrorResult(`Unknown operation: ${operation}`, { operation }, Date.now() - startTime);
            }
            return this.createSuccessResult(result, {
                operation,
                rowCount: dataRows.length,
                columnCount: headers.length,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`CSV processing failed: ${error instanceof Error ? error.message : String(error)}`, { operation: parameters.operation }, Date.now() - startTime);
        }
    }
    parseCSV(data, delimiter) {
        const rows = [];
        const lines = data.trim().split('\n');
        for (const line of lines) {
            if (line.trim()) {
                // Simple CSV parsing - would use a proper CSV parser in production
                const row = line.split(delimiter).map(cell => cell.trim().replace(/^"|"$/g, ''));
                rows.push(row);
            }
        }
        return rows;
    }
    validateCSV(headers, rows) {
        const issues = [];
        const columnCount = headers.length;
        // Check for consistent column count
        rows.forEach((row, index) => {
            if (row.length !== columnCount) {
                issues.push(`Row ${index + 1}: Expected ${columnCount} columns, got ${row.length}`);
            }
        });
        // Check for empty cells
        let emptyCells = 0;
        rows.forEach((row, rowIndex) => {
            row.forEach((cell, colIndex) => {
                if (!cell.trim()) {
                    emptyCells++;
                }
            });
        });
        return {
            valid: issues.length === 0,
            issues,
            statistics: {
                totalRows: rows.length,
                totalColumns: columnCount,
                emptyCells,
                completeness: Math.round(((rows.length * columnCount - emptyCells) / (rows.length * columnCount)) * 100),
            },
        };
    }
    filterCSV(headers, rows, params) {
        const { filterColumn, filterValue } = params;
        if (!filterColumn || filterValue === undefined) {
            throw new Error('Filter column and value are required for filter operation');
        }
        const columnIndex = headers.indexOf(filterColumn);
        if (columnIndex === -1) {
            throw new Error(`Column '${filterColumn}' not found`);
        }
        const filteredRows = rows.filter(row => row[columnIndex] && row[columnIndex].toLowerCase().includes(filterValue.toLowerCase()));
        return {
            headers,
            filteredRows,
            originalCount: rows.length,
            filteredCount: filteredRows.length,
            csv: this.generateCSV(headers, filteredRows, ','),
        };
    }
    sortCSV(headers, rows, params) {
        const { sortColumn, sortOrder } = params;
        if (!sortColumn) {
            throw new Error('Sort column is required for sort operation');
        }
        const columnIndex = headers.indexOf(sortColumn);
        if (columnIndex === -1) {
            throw new Error(`Column '${sortColumn}' not found`);
        }
        const sortedRows = [...rows].sort((a, b) => {
            const aVal = a[columnIndex] || '';
            const bVal = b[columnIndex] || '';
            // Try numeric comparison first
            const aNum = parseFloat(aVal);
            const bNum = parseFloat(bVal);
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return sortOrder === 'asc' ? aNum - bNum : bNum - aNum;
            }
            // String comparison
            return sortOrder === 'asc'
                ? aVal.localeCompare(bVal)
                : bVal.localeCompare(aVal);
        });
        return {
            headers,
            sortedRows,
            sortColumn,
            sortOrder,
            csv: this.generateCSV(headers, sortedRows, ','),
        };
    }
    aggregateCSV(headers, rows, params) {
        const { aggregateColumn, aggregateFunction } = params;
        if (!aggregateColumn || !aggregateFunction) {
            throw new Error('Aggregate column and function are required for aggregate operation');
        }
        const columnIndex = headers.indexOf(aggregateColumn);
        if (columnIndex === -1) {
            throw new Error(`Column '${aggregateColumn}' not found`);
        }
        const values = rows
            .map(row => parseFloat(row[columnIndex]))
            .filter(val => !isNaN(val));
        if (values.length === 0) {
            throw new Error(`No numeric values found in column '${aggregateColumn}'`);
        }
        let result;
        switch (aggregateFunction) {
            case 'sum':
                result = values.reduce((sum, val) => sum + val, 0);
                break;
            case 'avg':
                result = values.reduce((sum, val) => sum + val, 0) / values.length;
                break;
            case 'count':
                result = values.length;
                break;
            case 'min':
                result = Math.min(...values);
                break;
            case 'max':
                result = Math.max(...values);
                break;
            default:
                throw new Error(`Unknown aggregate function: ${aggregateFunction}`);
        }
        return {
            column: aggregateColumn,
            function: aggregateFunction,
            result,
            valueCount: values.length,
            totalRows: rows.length,
        };
    }
    generateCSV(headers, rows, delimiter) {
        const allRows = [headers, ...rows];
        return allRows.map(row => row.map(cell => cell.includes(delimiter) || cell.includes('"') || cell.includes('\n')
            ? `"${cell.replace(/"/g, '""')}"`
            : cell).join(delimiter)).join('\n');
    }
}
//# sourceMappingURL=data-tools.js.map