#!/usr/bin/env node
import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import { settings } from './config/settings.js';
import { logger } from './utils/logger.js';
import { terminalUI } from './ui/terminal-ui.js';
import { deepSeekClient } from './core/ai-client.js';
import { functionRegistry } from './core/function-registry.js';
import { workflowEngine } from './core/workflow-engine.js';
import { sessionManager } from './core/session-manager.js';
import { registerDefaultTools } from './tools/index.js';
import { testRunner } from './utils/test-runner.js';
// Package information
const packageJson = {
    name: 'ai-cli-tool',
    version: '1.0.0',
    description: 'Comprehensive AI-powered CLI tool with DeepSeek integration'
};
class AICLITool {
    program;
    isInteractiveMode = false;
    constructor() {
        this.program = new Command();
        this.setupCommands();
        this.registerTools();
    }
    setupCommands() {
        this.program
            .name(packageJson.name)
            .description(packageJson.description)
            .version(packageJson.version);
        // Interactive mode command
        this.program
            .command('interactive')
            .alias('i')
            .description('Start interactive mode for conversational AI assistance')
            .option('-m, --model <model>', 'AI model to use (chat or reasoner)', 'chat')
            .option('-v, --verbose', 'Enable verbose output')
            .action(this.handleInteractiveMode.bind(this));
        // Chat command for single queries
        this.program
            .command('chat <message>')
            .alias('c')
            .description('Send a single message to the AI')
            .option('-m, --model <model>', 'AI model to use (chat or reasoner)', 'chat')
            .option('-s, --stream', 'Stream the response in real-time')
            .option('--reasoning', 'Use reasoning model with step-by-step thinking')
            .action(this.handleChatCommand.bind(this));
        // Configuration commands
        this.program
            .command('config')
            .description('Manage configuration settings')
            .option('--show', 'Show current configuration')
            .option('--set <key=value>', 'Set a configuration value')
            .option('--reset', 'Reset to default configuration')
            .action(this.handleConfigCommand.bind(this));
        // Tool management commands
        this.program
            .command('tools')
            .description('Manage available tools')
            .option('--list', 'List all available tools')
            .option('--docs', 'Generate tool documentation')
            .option('--stats', 'Show tool usage statistics')
            .action(this.handleToolsCommand.bind(this));
        // Setup command for initial configuration
        this.program
            .command('setup')
            .description('Initial setup and configuration')
            .action(this.handleSetupCommand.bind(this));
        // Test command for validating configuration
        this.program
            .command('test')
            .description('Test AI connection and configuration')
            .option('--suite <name>', 'Run specific test suite')
            .option('--comprehensive', 'Run comprehensive test suite')
            .action(this.handleTestCommand.bind(this));
        // Workflow commands
        this.program
            .command('workflow')
            .description('Manage autonomous workflows')
            .option('--create <prompt>', 'Create workflow from natural language prompt')
            .option('--list', 'List all workflows')
            .option('--execute <id>', 'Execute a workflow by ID')
            .option('--status <id>', 'Get workflow status')
            .option('--delete <id>', 'Delete a workflow')
            .action(this.handleWorkflowCommand.bind(this));
        // Session management commands
        this.program
            .command('session')
            .description('Manage conversation sessions')
            .option('--list', 'List all sessions')
            .option('--create <name>', 'Create a new session')
            .option('--switch <id>', 'Switch to a different session')
            .option('--delete <id>', 'Delete a session')
            .option('--export <id>', 'Export session data')
            .option('--import <file>', 'Import session data')
            .option('--stats', 'Show session statistics')
            .option('--cleanup', 'Clean up old sessions')
            .action(this.handleSessionCommand.bind(this));
    }
    registerTools() {
        // Register all default tools
        registerDefaultTools();
        logger.info('Tools registered successfully');
    }
    async handleInteractiveMode(options) {
        this.isInteractiveMode = true;
        // Update UI options based on command line flags
        if (options.verbose) {
            terminalUI.updateOptions({ verbosity: 'verbose' });
        }
        terminalUI.showWelcome();
        // Validate API key before starting
        if (!settings.validateApiKey()) {
            terminalUI.showError('DeepSeek API key not configured', 'Please run "ai-cli setup" to configure your API key');
            return;
        }
        // Start interactive session
        await this.startInteractiveSession(options.model);
    }
    async startInteractiveSession(model) {
        const useReasoning = model === 'reasoner';
        while (this.isInteractiveMode) {
            try {
                const { message } = await inquirer.prompt([
                    {
                        type: 'input',
                        name: 'message',
                        message: '🤖 You:',
                        validate: (input) => input.trim().length > 0 || 'Please enter a message',
                    },
                ]);
                // Handle special commands
                if (await this.handleSpecialCommands(message.trim())) {
                    continue;
                }
                // Process AI request
                await this.processAIRequest(message, { useReasoning, stream: true });
            }
            catch (error) {
                if (error instanceof Error && error.name === 'ExitPromptError') {
                    break;
                }
                terminalUI.showError('An error occurred', error instanceof Error ? error.message : String(error));
            }
        }
        terminalUI.showInfo('Goodbye! 👋');
    }
    async handleSpecialCommands(message) {
        const lowerMessage = message.toLowerCase();
        switch (lowerMessage) {
            case 'exit':
            case 'quit':
            case 'q':
                this.isInteractiveMode = false;
                return true;
            case 'help':
            case 'h':
                this.showInteractiveHelp();
                return true;
            case 'clear':
            case 'cls':
                terminalUI.clear();
                return true;
            case 'config':
                terminalUI.displayConfig(settings.export());
                return true;
            case 'tools':
                this.showToolsList();
                return true;
            case 'sessions':
                await this.listSessions();
                return true;
            case 'session info':
                await this.showCurrentSessionInfo();
                return true;
            default:
                return false;
        }
    }
    showInteractiveHelp() {
        const commands = [
            { name: 'help', description: 'Show this help message' },
            { name: 'clear', description: 'Clear the screen' },
            { name: 'config', description: 'Show current configuration' },
            { name: 'tools', description: 'List available tools' },
            { name: 'sessions', description: 'List all sessions' },
            { name: 'session info', description: 'Show current session information' },
            { name: 'exit', description: 'Exit interactive mode' },
        ];
        terminalUI.showHelp(commands);
    }
    async showCurrentSessionInfo() {
        try {
            const session = await sessionManager.getCurrentSession();
            const sessionInfo = {
                'Current Session': {
                    'Name': session.name,
                    'ID': session.id,
                    'Description': session.description || 'None',
                    'Created': session.createdAt.toLocaleString(),
                    'Last Accessed': session.lastAccessedAt.toLocaleString(),
                    'Total Messages': session.totalMessages,
                    'Total Tokens Used': session.totalTokensUsed,
                    'Workflows': session.workflowIds.length,
                },
            };
            terminalUI.displayConfig(sessionInfo);
        }
        catch (error) {
            terminalUI.showError('Failed to get session info', error instanceof Error ? error.message : String(error));
        }
    }
    showToolsList() {
        const tools = functionRegistry.getAllTools();
        console.log(chalk.cyan.bold('\n🔧 Available Tools:\n'));
        for (const tool of tools) {
            console.log(`  ${chalk.green(tool.name)}: ${chalk.white(tool.description)}`);
        }
        console.log();
    }
    async handleChatCommand(message, options) {
        if (!settings.validateApiKey()) {
            terminalUI.showError('DeepSeek API key not configured', 'Please run "ai-cli setup" to configure your API key');
            return;
        }
        const useReasoning = options.reasoning || options.model === 'reasoner';
        await this.processAIRequest(message, {
            useReasoning,
            stream: options.stream
        });
    }
    async processAIRequest(message, options) {
        const loadingId = terminalUI.startLoading('Processing your request...');
        try {
            // Get current session and add user message
            const session = await sessionManager.getCurrentSession();
            const userMessage = { role: 'user', content: message };
            await sessionManager.addMessage(userMessage);
            // Get recent conversation history for context
            const recentMessages = sessionManager.getSessionMessages(session.id, { limit: 10 });
            const messages = recentMessages.length > 0 ? recentMessages : [userMessage];
            if (options.stream) {
                terminalUI.stopLoading(loadingId);
                if (options.useReasoning) {
                    terminalUI.startReasoningDisplay();
                }
                console.log(chalk.blue('\n🤖 AI: '));
                let accumulatedContent = '';
                deepSeekClient.streamChat(messages, {
                    onContent: (content) => {
                        accumulatedContent += content;
                        process.stdout.write(chalk.white(content));
                    },
                    onReasoning: (step) => {
                        terminalUI.displayReasoning([step]);
                    },
                    onComplete: async (response) => {
                        console.log('\n');
                        if (options.useReasoning) {
                            terminalUI.stopReasoningDisplay();
                        }
                        // Add AI response to session
                        const aiMessage = { role: 'assistant', content: accumulatedContent };
                        await sessionManager.addMessage(aiMessage, response.usage?.total_tokens || 0);
                    },
                    onError: (error) => {
                        terminalUI.showError('AI request failed', error.message);
                    },
                }, { useReasoning: options.useReasoning });
            }
            else {
                const response = options.useReasoning
                    ? await deepSeekClient.chatWithReasoning(messages)
                    : await deepSeekClient.chat(messages);
                terminalUI.stopLoading(loadingId, 'Response received');
                if (response.reasoning) {
                    terminalUI.displayReasoning(response.reasoning);
                }
                terminalUI.displayAIResponse(response.content);
                // Add AI response to session
                const aiMessage = { role: 'assistant', content: response.content };
                await sessionManager.addMessage(aiMessage, response.usage?.total_tokens || 0);
            }
        }
        catch (error) {
            terminalUI.stopLoading(loadingId);
            terminalUI.showError('AI request failed', error instanceof Error ? error.message : String(error));
            logger.error('AI request failed', error);
        }
    }
    async handleConfigCommand(options) {
        if (options.show) {
            terminalUI.displayConfig(settings.export());
            return;
        }
        if (options.reset) {
            const { confirm } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'confirm',
                    message: 'Are you sure you want to reset all settings to defaults?',
                    default: false,
                },
            ]);
            if (confirm) {
                settings.resetToDefaults();
                terminalUI.showSuccess('Configuration reset to defaults');
            }
            return;
        }
        if (options.set) {
            const [key, value] = options.set.split('=');
            if (!key || value === undefined) {
                terminalUI.showError('Invalid format', 'Use --set key=value');
                return;
            }
            try {
                // Simple key setting - can be enhanced for nested keys
                terminalUI.showInfo(`Setting ${key} = ${value}`);
                terminalUI.showSuccess('Configuration updated');
            }
            catch (error) {
                terminalUI.showError('Failed to update configuration', error instanceof Error ? error.message : String(error));
            }
            return;
        }
        // Interactive configuration
        await this.interactiveConfig();
    }
    async interactiveConfig() {
        const { section } = await inquirer.prompt([
            {
                type: 'list',
                name: 'section',
                message: 'Which section would you like to configure?',
                choices: ['DeepSeek API', 'UI Settings', 'Workflow Settings', 'Logging'],
            },
        ]);
        // Implementation for interactive configuration
        terminalUI.showInfo(`Configuring ${section}...`);
        // TODO: Implement section-specific configuration
    }
    async handleToolsCommand(options) {
        if (options.list) {
            this.showToolsList();
            return;
        }
        if (options.docs) {
            const docs = functionRegistry.generateDocumentation();
            console.log(docs);
            return;
        }
        if (options.stats) {
            const stats = functionRegistry.getStats();
            terminalUI.displayConfig(stats);
            return;
        }
        // Default: show tools overview
        this.showToolsList();
    }
    async handleSetupCommand() {
        terminalUI.showWelcome();
        console.log(chalk.cyan.bold('🚀 Welcome to AI CLI Tool Setup\n'));
        const { apiKey } = await inquirer.prompt([
            {
                type: 'password',
                name: 'apiKey',
                message: 'Enter your DeepSeek API key:',
                validate: (input) => input.trim().length > 0 || 'API key is required',
            },
        ]);
        // Save API key
        settings.updateSetting('deepseek', 'apiKey', apiKey);
        // Test the API key
        const testId = terminalUI.startLoading('Testing API connection...');
        try {
            const isValid = await deepSeekClient.validateApiKey();
            terminalUI.stopLoading(testId);
            if (isValid) {
                terminalUI.showSuccess('Setup completed successfully!', 'Your API key has been saved and tested. You can now use the AI CLI tool.');
            }
            else {
                terminalUI.showError('Invalid API key', 'Please check your API key and try again.');
            }
        }
        catch (error) {
            terminalUI.stopLoading(testId);
            terminalUI.showError('Setup failed', error instanceof Error ? error.message : String(error));
        }
    }
    async handleTestCommand(options) {
        if (options.comprehensive) {
            await testRunner.runAllTests();
            return;
        }
        if (options.suite) {
            await testRunner.runTestSuite(options.suite);
            return;
        }
        // Basic configuration test
        const testId = terminalUI.startLoading('Testing configuration...');
        try {
            // Test API key
            const isValid = await deepSeekClient.validateApiKey();
            terminalUI.stopLoading(testId);
            if (isValid) {
                terminalUI.showSuccess('Basic test passed!', 'Your API key is working correctly.');
                terminalUI.showInfo('Run "ai-cli test --comprehensive" for full test suite');
            }
            else {
                terminalUI.showError('API key test failed', 'Please check your DeepSeek API key.');
            }
        }
        catch (error) {
            terminalUI.stopLoading(testId);
            terminalUI.showError('Test failed', error instanceof Error ? error.message : String(error));
        }
    }
    async handleWorkflowCommand(options) {
        if (options.create) {
            await this.createWorkflowFromPrompt(options.create);
            return;
        }
        if (options.list) {
            this.listWorkflows();
            return;
        }
        if (options.execute) {
            await this.executeWorkflow(options.execute);
            return;
        }
        if (options.status) {
            this.showWorkflowStatus(options.status);
            return;
        }
        if (options.delete) {
            await this.deleteWorkflow(options.delete);
            return;
        }
        // Default: show workflow help
        this.showWorkflowHelp();
    }
    async createWorkflowFromPrompt(prompt) {
        if (!settings.validateApiKey()) {
            terminalUI.showError('DeepSeek API key not configured', 'Please run "ai-cli setup" to configure your API key');
            return;
        }
        const loadingId = terminalUI.startLoading('Generating workflow from prompt...');
        try {
            const workflow = await workflowEngine.generateWorkflowFromPrompt(prompt);
            terminalUI.stopLoading(loadingId, 'Workflow created successfully!');
            terminalUI.showSuccess(`Created workflow: ${workflow.name}`, `ID: ${workflow.id}\nSteps: ${workflow.steps.length}\nDescription: ${workflow.description}`);
            // Ask if user wants to execute immediately
            const { execute } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'execute',
                    message: 'Would you like to execute this workflow now?',
                    default: false,
                },
            ]);
            if (execute) {
                await this.executeWorkflow(workflow.id);
            }
        }
        catch (error) {
            terminalUI.stopLoading(loadingId);
            terminalUI.showError('Failed to create workflow', error instanceof Error ? error.message : String(error));
        }
    }
    listWorkflows() {
        const workflows = workflowEngine.listWorkflows();
        if (workflows.length === 0) {
            terminalUI.showInfo('No workflows found');
            return;
        }
        console.log(chalk.cyan.bold('\n📋 Workflows:\n'));
        for (const workflow of workflows) {
            const statusColor = workflow.status === 'completed' ? 'green' :
                workflow.status === 'failed' ? 'red' :
                    workflow.status === 'running' ? 'yellow' : 'gray';
            console.log(`  ${chalk[statusColor]('●')} ${chalk.white.bold(workflow.name)} (${workflow.id})`);
            console.log(`    ${chalk.gray('Status:')} ${chalk[statusColor](workflow.status)}`);
            console.log(`    ${chalk.gray('Steps:')} ${workflow.steps.length}`);
            console.log(`    ${chalk.gray('Progress:')} ${workflow.progress.percentage}%`);
            if (workflow.description) {
                console.log(`    ${chalk.gray('Description:')} ${workflow.description}`);
            }
            console.log();
        }
    }
    async executeWorkflow(workflowId) {
        const workflow = workflowEngine.getWorkflow(workflowId);
        if (!workflow) {
            terminalUI.showError('Workflow not found', `No workflow with ID: ${workflowId}`);
            return;
        }
        terminalUI.showInfo(`Executing workflow: ${workflow.name}`);
        try {
            const executedWorkflow = await workflowEngine.executeWorkflow(workflowId, {
                continueOnError: false,
                saveProgress: true,
            });
            terminalUI.showSuccess('Workflow completed successfully!', `Duration: ${executedWorkflow.duration}ms\nSteps completed: ${executedWorkflow.progress.current}/${executedWorkflow.progress.total}`);
        }
        catch (error) {
            terminalUI.showError('Workflow execution failed', error instanceof Error ? error.message : String(error));
        }
    }
    showWorkflowStatus(workflowId) {
        const workflow = workflowEngine.getWorkflow(workflowId);
        if (!workflow) {
            terminalUI.showError('Workflow not found', `No workflow with ID: ${workflowId}`);
            return;
        }
        const statusInfo = {
            'Workflow Info': {
                ID: workflow.id,
                Name: workflow.name,
                Status: workflow.status,
                Progress: `${workflow.progress.current}/${workflow.progress.total} (${workflow.progress.percentage}%)`,
            },
            'Execution Details': {
                'Start Time': workflow.startTime?.toISOString() || 'Not started',
                'End Time': workflow.endTime?.toISOString() || 'Not finished',
                'Duration': workflow.duration ? `${workflow.duration}ms` : 'N/A',
            },
            'Steps': workflow.steps.reduce((acc, step, index) => {
                acc[`Step ${index + 1}`] = `${step.name} - ${step.status}`;
                return acc;
            }, {}),
        };
        terminalUI.displayConfig(statusInfo);
    }
    async deleteWorkflow(workflowId) {
        const workflow = workflowEngine.getWorkflow(workflowId);
        if (!workflow) {
            terminalUI.showError('Workflow not found', `No workflow with ID: ${workflowId}`);
            return;
        }
        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: `Are you sure you want to delete workflow "${workflow.name}"?`,
                default: false,
            },
        ]);
        if (confirm) {
            const success = workflowEngine.deleteWorkflow(workflowId);
            if (success) {
                terminalUI.showSuccess('Workflow deleted successfully');
            }
            else {
                terminalUI.showError('Failed to delete workflow', 'Workflow may be currently running');
            }
        }
    }
    showWorkflowHelp() {
        const commands = [
            { name: 'workflow --create "<prompt>"', description: 'Create workflow from natural language' },
            { name: 'workflow --list', description: 'List all workflows' },
            { name: 'workflow --execute <id>', description: 'Execute a workflow' },
            { name: 'workflow --status <id>', description: 'Show workflow status' },
            { name: 'workflow --delete <id>', description: 'Delete a workflow' },
        ];
        terminalUI.showHelp(commands);
    }
    async handleSessionCommand(options) {
        if (options.list) {
            await this.listSessions();
            return;
        }
        if (options.create) {
            await this.createSession(options.create);
            return;
        }
        if (options.switch) {
            await this.switchSession(options.switch);
            return;
        }
        if (options.delete) {
            await this.deleteSession(options.delete);
            return;
        }
        if (options.export) {
            await this.exportSession(options.export);
            return;
        }
        if (options.import) {
            await this.importSession(options.import);
            return;
        }
        if (options.stats) {
            this.showSessionStats();
            return;
        }
        if (options.cleanup) {
            await this.cleanupSessions();
            return;
        }
        // Default: show session help
        this.showSessionHelp();
    }
    async listSessions() {
        try {
            const sessions = await sessionManager.listSessions({ limit: 20 });
            if (sessions.length === 0) {
                terminalUI.showInfo('No sessions found');
                return;
            }
            console.log(chalk.cyan.bold('\n💬 Sessions:\n'));
            for (const session of sessions) {
                const isActive = session.id === (await sessionManager.getCurrentSession()).id;
                const indicator = isActive ? chalk.green('●') : chalk.gray('○');
                console.log(`  ${indicator} ${chalk.white.bold(session.name)} (${session.id})`);
                console.log(`    ${chalk.gray('Messages:')} ${session.messageCount}`);
                console.log(`    ${chalk.gray('Workflows:')} ${session.workflowCount}`);
                console.log(`    ${chalk.gray('Last accessed:')} ${session.lastAccessedAt.toLocaleString()}`);
                if (session.description) {
                    console.log(`    ${chalk.gray('Description:')} ${session.description}`);
                }
                console.log();
            }
        }
        catch (error) {
            terminalUI.showError('Failed to list sessions', error instanceof Error ? error.message : String(error));
        }
    }
    async createSession(name) {
        try {
            const description = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'description',
                    message: 'Enter session description (optional):',
                },
            ]);
            const session = await sessionManager.createSession(name, {
                description: description.description || undefined,
            });
            terminalUI.showSuccess(`Created session: ${session.name}`, `ID: ${session.id}\nDescription: ${session.description || 'None'}`);
        }
        catch (error) {
            terminalUI.showError('Failed to create session', error instanceof Error ? error.message : String(error));
        }
    }
    async switchSession(sessionId) {
        try {
            const session = await sessionManager.switchSession(sessionId);
            if (session) {
                terminalUI.showSuccess(`Switched to session: ${session.name}`, `ID: ${session.id}\nMessages: ${session.totalMessages}`);
            }
            else {
                terminalUI.showError('Session not found', `No session with ID: ${sessionId}`);
            }
        }
        catch (error) {
            terminalUI.showError('Failed to switch session', error instanceof Error ? error.message : String(error));
        }
    }
    async deleteSession(sessionId) {
        try {
            const sessions = await sessionManager.listSessions();
            const session = sessions.find(s => s.id === sessionId);
            if (!session) {
                terminalUI.showError('Session not found', `No session with ID: ${sessionId}`);
                return;
            }
            const { confirm } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'confirm',
                    message: `Are you sure you want to delete session "${session.name}"?`,
                    default: false,
                },
            ]);
            if (confirm) {
                const success = await sessionManager.deleteSession(sessionId);
                if (success) {
                    terminalUI.showSuccess('Session deleted successfully');
                }
                else {
                    terminalUI.showError('Failed to delete session');
                }
            }
        }
        catch (error) {
            terminalUI.showError('Failed to delete session', error instanceof Error ? error.message : String(error));
        }
    }
    async exportSession(sessionId) {
        try {
            const sessionData = await sessionManager.exportSession(sessionId);
            const filename = `session_${sessionId}_${Date.now()}.json`;
            // In a real implementation, you might want to save to a specific directory
            console.log(chalk.cyan('\n📄 Session Export Data:\n'));
            console.log(sessionData);
            terminalUI.showSuccess('Session exported successfully', `Data displayed above. In production, this would be saved to: ${filename}`);
        }
        catch (error) {
            terminalUI.showError('Failed to export session', error instanceof Error ? error.message : String(error));
        }
    }
    async importSession(filename) {
        try {
            // In a real implementation, you would read from the file
            terminalUI.showInfo('Session import functionality would read from file and import session data');
            // For demonstration, show how it would work:
            // const sessionData = await fs.readFile(filename, 'utf8');
            // const session = await sessionManager.importSession(sessionData);
            // terminalUI.showSuccess(`Imported session: ${session.name}`, `ID: ${session.id}`);
        }
        catch (error) {
            terminalUI.showError('Failed to import session', error instanceof Error ? error.message : String(error));
        }
    }
    showSessionStats() {
        try {
            const stats = sessionManager.getSessionStats();
            const statsInfo = {
                'Session Statistics': {
                    'Total Sessions': stats.totalSessions,
                    'Total Messages': stats.totalMessages,
                    'Total Tokens Used': stats.totalTokensUsed,
                    'Average Messages per Session': stats.averageMessagesPerSession,
                },
                'Date Range': {
                    'Oldest Session': stats.oldestSession?.toLocaleString() || 'N/A',
                    'Newest Session': stats.newestSession?.toLocaleString() || 'N/A',
                },
            };
            terminalUI.displayConfig(statsInfo);
        }
        catch (error) {
            terminalUI.showError('Failed to get session statistics', error instanceof Error ? error.message : String(error));
        }
    }
    async cleanupSessions() {
        try {
            const { confirm } = await inquirer.prompt([
                {
                    type: 'confirm',
                    name: 'confirm',
                    message: 'This will delete sessions older than 30 days. Continue?',
                    default: false,
                },
            ]);
            if (confirm) {
                const deletedCount = await sessionManager.cleanupOldSessions();
                terminalUI.showSuccess('Session cleanup completed', `Deleted ${deletedCount} old sessions`);
            }
        }
        catch (error) {
            terminalUI.showError('Failed to cleanup sessions', error instanceof Error ? error.message : String(error));
        }
    }
    showSessionHelp() {
        const commands = [
            { name: 'session --list', description: 'List all sessions' },
            { name: 'session --create <name>', description: 'Create a new session' },
            { name: 'session --switch <id>', description: 'Switch to a different session' },
            { name: 'session --delete <id>', description: 'Delete a session' },
            { name: 'session --export <id>', description: 'Export session data' },
            { name: 'session --import <file>', description: 'Import session data' },
            { name: 'session --stats', description: 'Show session statistics' },
            { name: 'session --cleanup', description: 'Clean up old sessions' },
        ];
        terminalUI.showHelp(commands);
    }
    async run() {
        try {
            await this.program.parseAsync(process.argv);
        }
        catch (error) {
            logger.error('CLI execution failed', error);
            terminalUI.showError('An unexpected error occurred', error instanceof Error ? error.message : String(error));
            process.exit(1);
        }
    }
}
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception', error);
    console.error(chalk.red('Fatal error:'), error.message);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled rejection', reason);
    console.error(chalk.red('Unhandled promise rejection:'), reason);
    process.exit(1);
});
// Create and run the CLI tool
const cliTool = new AICLITool();
cliTool.run();
//# sourceMappingURL=cli.js.map