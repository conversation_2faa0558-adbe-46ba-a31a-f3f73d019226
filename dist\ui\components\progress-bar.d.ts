export interface ProgressBarOptions {
    width: number;
    complete: string;
    incomplete: string;
    showPercentage: boolean;
    showValue: boolean;
    showEta: boolean;
    theme: 'default' | 'minimal' | 'detailed';
    color: string;
}
export interface ProgressUpdate {
    current: number;
    total: number;
    message?: string;
    metadata?: Record<string, any>;
}
export declare class ProgressBar {
    private options;
    private startTime;
    private lastUpdate;
    private isComplete;
    private current;
    private total;
    private message;
    constructor(options?: Partial<ProgressBarOptions>);
    update(update: ProgressUpdate): void;
    complete(finalMessage?: string): void;
    increment(amount?: number, message?: string): void;
    setTotal(total: number, resetCurrent?: boolean): void;
    getPercentage(): number;
    getElapsedTime(): number;
    getETA(): number;
    private formatTime;
    private render;
    private createBar;
    private renderMinimal;
    private renderDefault;
    private renderDetailed;
    static simulate(duration: number, steps?: number, message?: string, options?: Partial<ProgressBarOptions>): Promise<void>;
}
export declare class MultiProgressBar {
    private progressBars;
    private isRendering;
    private renderInterval;
    addProgressBar(id: string, options?: Partial<ProgressBarOptions>): ProgressBar;
    updateProgressBar(id: string, update: ProgressUpdate): void;
    completeProgressBar(id: string, finalMessage?: string): void;
    removeProgressBar(id: string): void;
    getProgressBar(id: string): ProgressBar | undefined;
    clear(): void;
    private startRendering;
    private stopRendering;
    private renderAll;
}
export declare function createFileProgressBar(filename: string): ProgressBar;
export declare function createDownloadProgressBar(): ProgressBar;
export declare function createTaskProgressBar(taskName: string): ProgressBar;
//# sourceMappingURL=progress-bar.d.ts.map