// File Operations Tools
export { <PERSON>ReadTool, FileWriteTool, DirectoryListTool, FileDeleteTool, } from './file-operations.js';
// System Tools
export { SystemCommandTool, SystemInfoTool, EnvironmentTool, } from './system-tools.js';
// Web Tools
export { WebFetchTool, WebSearchTool, UrlAnalysisTool, } from './web-tools.js';
// Data Processing Tools
export { JsonProcessorTool, CsvProcessorTool, } from './data-tools.js';
// Base Tool Interface
export { BaseTool, createParameter, createExample, } from './base-tool.js';
// Tool Registry
import { functionRegistry } from '../core/function-registry.js';
import { FileReadTool, FileWriteTool, DirectoryListTool, FileDeleteTool, } from './file-operations.js';
import { SystemCommandTool, SystemInfoTool, EnvironmentTool, } from './system-tools.js';
import { WebFetchTool, WebSearchTool, UrlAnalysisTool, } from './web-tools.js';
import { JsonProcessorTool, CsvProcessorTool, } from './data-tools.js';
// Function to register all default tools
export function registerDefaultTools() {
    // File operation tools
    functionRegistry.registerTool(new FileReadTool(), 'file');
    functionRegistry.registerTool(new FileWriteTool(), 'file');
    functionRegistry.registerTool(new DirectoryListTool(), 'file');
    functionRegistry.registerTool(new FileDeleteTool(), 'file');
    // System tools
    functionRegistry.registerTool(new SystemCommandTool(), 'system');
    functionRegistry.registerTool(new SystemInfoTool(), 'system');
    functionRegistry.registerTool(new EnvironmentTool(), 'system');
    // Web tools
    functionRegistry.registerTool(new WebFetchTool(), 'web');
    functionRegistry.registerTool(new WebSearchTool(), 'web');
    functionRegistry.registerTool(new UrlAnalysisTool(), 'web');
    // Data processing tools
    functionRegistry.registerTool(new JsonProcessorTool(), 'data');
    functionRegistry.registerTool(new CsvProcessorTool(), 'data');
}
// Export the function registry for convenience
export { functionRegistry } from '../core/function-registry.js';
//# sourceMappingURL=index.js.map