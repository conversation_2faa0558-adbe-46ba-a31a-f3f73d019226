import chalk from 'chalk';
export class BallAnimation {
    frames = ['●', '○', '◐', '◑', '◒', '◓'];
    currentFrame = 0;
    interval = null;
    startTime = null;
    options;
    isRunning = false;
    constructor(options = {}) {
        this.options = {
            speed: 200,
            color: 'cyan',
            showTimer: true,
            prefix: '',
            suffix: '',
            ...options,
        };
    }
    start(message) {
        if (this.isRunning) {
            return;
        }
        this.isRunning = true;
        this.startTime = new Date();
        this.currentFrame = 0;
        // Hide cursor
        process.stdout.write('\x1B[?25l');
        this.interval = setInterval(() => {
            this.render(message);
            this.currentFrame = (this.currentFrame + 1) % this.frames.length;
        }, this.options.speed);
    }
    stop(finalMessage) {
        if (!this.isRunning) {
            return;
        }
        this.isRunning = false;
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        // Clear the line and show final message
        process.stdout.write('\r\x1B[K');
        if (finalMessage) {
            console.log(finalMessage);
        }
        // Show cursor
        process.stdout.write('\x1B[?25h');
    }
    updateMessage(message) {
        if (this.isRunning) {
            this.render(message);
        }
    }
    getElapsedTime() {
        if (!this.startTime) {
            return 0;
        }
        return Date.now() - this.startTime.getTime();
    }
    getElapsedTimeFormatted() {
        const elapsed = this.getElapsedTime();
        const seconds = Math.floor(elapsed / 1000);
        const milliseconds = elapsed % 1000;
        if (seconds > 0) {
            return `${seconds}.${Math.floor(milliseconds / 100)}s`;
        }
        else {
            return `${milliseconds}ms`;
        }
    }
    render(message) {
        const frame = this.frames[this.currentFrame];
        const coloredFrame = chalk[this.options.color](frame);
        let output = this.options.prefix + coloredFrame;
        if (message) {
            output += ` ${message}`;
        }
        if (this.options.showTimer && this.startTime) {
            const elapsed = this.getElapsedTimeFormatted();
            output += chalk.gray(` (${elapsed})`);
        }
        output += this.options.suffix;
        // Clear line and write new content
        process.stdout.write('\r\x1B[K' + output);
    }
    // Static method for simple one-time animations
    static async animate(duration, message, options = {}) {
        return new Promise((resolve) => {
            const animation = new BallAnimation(options);
            animation.start(message);
            setTimeout(() => {
                animation.stop();
                resolve();
            }, duration);
        });
    }
    // Method to create a promise that resolves when animation is stopped
    waitForStop() {
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (!this.isRunning) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 50);
        });
    }
}
// Utility class for managing multiple animations
export class AnimationManager {
    animations = new Map();
    createAnimation(id, options = {}) {
        const animation = new BallAnimation(options);
        this.animations.set(id, animation);
        return animation;
    }
    startAnimation(id, message) {
        const animation = this.animations.get(id);
        if (animation) {
            animation.start(message);
            return true;
        }
        return false;
    }
    stopAnimation(id, finalMessage) {
        const animation = this.animations.get(id);
        if (animation) {
            animation.stop(finalMessage);
            return true;
        }
        return false;
    }
    updateAnimation(id, message) {
        const animation = this.animations.get(id);
        if (animation) {
            animation.updateMessage(message);
            return true;
        }
        return false;
    }
    stopAll(finalMessage) {
        for (const animation of this.animations.values()) {
            animation.stop(finalMessage);
        }
    }
    removeAnimation(id) {
        const animation = this.animations.get(id);
        if (animation) {
            animation.stop();
            this.animations.delete(id);
            return true;
        }
        return false;
    }
    getAnimation(id) {
        return this.animations.get(id);
    }
    getAllAnimations() {
        return Array.from(this.animations.values());
    }
    clear() {
        this.stopAll();
        this.animations.clear();
    }
}
// Export a default instance for convenience
export const animationManager = new AnimationManager();
//# sourceMappingURL=ball-animation.js.map