{"version": 3, "file": "system-tools.js", "sourceRoot": "", "sources": ["../../src/tools/system-tools.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAA6C,eAAe,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAErH,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAElC,sBAAsB;AACtB,MAAM,OAAO,iBAAkB,SAAQ,QAAQ;IACpC,IAAI,GAAG,gBAAgB,CAAC;IACxB,WAAW,GAAG,oDAAoD,CAAC;IACnE,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,OAAO,EAAE,eAAe,CAAC,QAAQ,EAAE,oBAAoB,CAAC;YACxD,OAAO,EAAE,eAAe,CAAC,QAAQ,EAAE,0CAA0C,CAAC;YAC9E,GAAG,EAAE,eAAe,CAAC,QAAQ,EAAE,oDAAoD,CAAC;YACpF,GAAG,EAAE,eAAe,CAAC,QAAQ,EAAE,yCAAyC,EAAE;gBACxE,UAAU,EAAE,EAAE;aACf,CAAC;SACH;QACD,QAAQ,EAAE,CAAC,SAAS,CAAC;KACtB,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,yBAAyB,EACzB,EAAE,OAAO,EAAE,QAAQ,EAAE,EACrB,6CAA6C,CAC9C;QACD,aAAa,CACX,wBAAwB,EACxB,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,EACtC,gDAAgD,CACjD;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAC7C,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;YAC1B,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;SACrC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;YAElD,8CAA8C;YAC9C,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,iBAAiB,CAC3B,sCAAsC,EACtC,EAAE,OAAO,EAAE,EACX,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,OAAO,GAAG;gBACd,OAAO;gBACP,GAAG,EAAE,GAAG,IAAI,OAAO,CAAC,gBAAgB;gBACpC,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,GAAG,GAAG,EAAE;gBACvD,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,aAAa;aACtC,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE7D,OAAO,IAAI,CAAC,mBAAmB,CAAC;gBAC9B,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACrB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACrB,OAAO;gBACP,QAAQ,EAAE,CAAC;aACZ,EAAE;gBACD,OAAO;gBACP,GAAG,EAAE,OAAO,CAAC,GAAG;gBAChB,OAAO;aACR,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,iBAAiB,CAC3B,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EACrF;gBACE,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;gBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;aAC3B,EACD,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACxC,MAAM,iBAAiB,GAAG;YACxB,eAAe,EAAE,WAAW;YAC5B,WAAW,EAAE,UAAU;YACvB,MAAM,EAAE,oBAAoB;YAC5B,UAAU,EAAE,kBAAkB;YAC9B,UAAU,EAAE,kBAAkB;YAC9B,QAAQ,EAAE,gBAAgB;YAC1B,MAAM,EAAE,cAAc;YACtB,UAAU,EAAE,WAAW;YACvB,UAAU,EAAE,SAAS;YACrB,aAAa,EAAE,YAAY;SAC5B,CAAC;QAEF,OAAO,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC;CACF;AAED,0BAA0B;AAC1B,MAAM,OAAO,cAAe,SAAQ,QAAQ;IACjC,IAAI,GAAG,aAAa,CAAC;IACrB,WAAW,GAAG,sCAAsC,CAAC;IACrD,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,QAAQ,EAAE,eAAe,CAAC,SAAS,EAAE,sDAAsD,CAAC;YAC5F,QAAQ,EAAE,eAAe,CAAC,OAAO,EAAE,8BAA8B,EAAE;gBACjE,KAAK,EAAE,eAAe,CAAC,QAAQ,EAAE,cAAc,EAAE;oBAC/C,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC;iBAC9D,CAAC;aACH,CAAC;SACH;QACD,QAAQ,EAAE,EAAE;KACb,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,mBAAmB,EACnB,EAAE,QAAQ,EAAE,KAAK,EAAE,EACnB,+CAA+C,CAChD;QACD,aAAa,CACX,8BAA8B,EAC9B,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAC/C,4CAA4C,CAC7C;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YACpC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;SAC9F,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC;YAC1C,MAAM,IAAI,GAAQ,EAAE,CAAC;YAErB,MAAM,iBAAiB,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAE9D,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,EAAE,GAAG;oBACR,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE;oBACvB,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE;oBACf,OAAO,EAAE,EAAE,CAAC,OAAO,EAAE;oBACrB,OAAO,EAAE,EAAE,CAAC,OAAO,EAAE;oBACrB,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE;oBACf,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE;oBACvB,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE;iBACpB,CAAC;YACJ,CAAC;YAED,IAAI,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,GAAG,GAAG;oBACT,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,SAAS;oBAClC,KAAK,EAAE,IAAI,CAAC,MAAM;oBAClB,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC;oBAC1B,GAAG,CAAC,QAAQ,IAAI;wBACd,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;4BACxB,KAAK,EAAE,GAAG,CAAC,KAAK;4BAChB,KAAK,EAAE,GAAG,CAAC,KAAK;4BAChB,KAAK,EAAE,GAAG,CAAC,KAAK;yBACjB,CAAC,CAAC;qBACJ,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,IAAI,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzC,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;gBAC/B,MAAM,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;gBAC7B,MAAM,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;gBAEnC,IAAI,CAAC,MAAM,GAAG;oBACZ,KAAK,EAAE,QAAQ;oBACf,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,OAAO;oBACb,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC;oBACpD,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;oBACvD,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;oBACrD,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;iBACtD,CAAC;YACJ,CAAC;YAED,IAAI,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,MAAM,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;gBACjD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE;oBAClF,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;wBACpC,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,MAAM,EAAE,KAAK,CAAC,MAAM;wBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;wBACxB,GAAG,CAAC,QAAQ,IAAI;4BACd,OAAO,EAAE,KAAK,CAAC,OAAO;4BACtB,GAAG,EAAE,KAAK,CAAC,GAAG;4BACd,IAAI,EAAE,KAAK,CAAC,IAAI;yBACjB,CAAC;qBACH,CAAC,CAAC,IAAI,EAAE,CAAC;oBACV,OAAO,GAAG,CAAC;gBACb,CAAC,EAAE,EAAS,CAAC,CAAC;YAChB,CAAC;YAED,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACnD,0DAA0D;gBAC1D,IAAI,CAAC;oBACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC5C,IAAI,CAAC,IAAI,GAAG;wBACV,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE;qBACrB,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;gBAC/D,CAAC;YACH,CAAC;YAED,IAAI,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACxD,IAAI,CAAC;oBACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,mBAAmB,CAAC,CAAC;oBACxD,IAAI,CAAC,SAAS,GAAG;wBACf,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE;qBACrB,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,EAAE,wCAAwC,EAAE,CAAC;gBACvE,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE;gBACpC,QAAQ;gBACR,QAAQ,EAAE,iBAAiB;gBAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,qCAAqC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAC7F,EAAE,EACF,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAED,6BAA6B;AAC7B,MAAM,OAAO,eAAgB,SAAQ,QAAQ;IAClC,IAAI,GAAG,kBAAkB,CAAC;IAC1B,WAAW,GAAG,sCAAsC,CAAC;IACrD,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,MAAM,EAAE,eAAe,CAAC,QAAQ,EAAE,mBAAmB,EAAE;gBACrD,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;aAC7B,CAAC;YACF,QAAQ,EAAE,eAAe,CAAC,QAAQ,EAAE,iDAAiD,CAAC;YACtF,KAAK,EAAE,eAAe,CAAC,QAAQ,EAAE,+BAA+B,CAAC;YACjE,MAAM,EAAE,eAAe,CAAC,QAAQ,EAAE,2CAA2C,CAAC;SAC/E;QACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;KACrB,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,mCAAmC,EACnC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,EACnC,oDAAoD,CACrD;QACD,aAAa,CACX,gCAAgC,EAChC,EAAE,MAAM,EAAE,MAAM,EAAE,EAClB,mCAAmC,CACpC;QACD,aAAa,CACX,qCAAqC,EACrC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAClC,iDAAiD,CAClD;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACtC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;YAC/B,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;YAC5B,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;SAC9B,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YACjB,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5C,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,EAAE;YACD,OAAO,EAAE,wFAAwF;SAClG,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;YAEvD,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,KAAK;oBACR,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACvC,OAAO,IAAI,CAAC,mBAAmB,CAAC;wBAC9B,QAAQ;wBACR,KAAK,EAAE,QAAQ,IAAI,IAAI;wBACvB,MAAM,EAAE,QAAQ,KAAK,SAAS;qBAC/B,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;gBAEnD,KAAK,MAAM;oBACT,IAAI,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;oBAEjC,IAAI,MAAM,EAAE,CAAC;wBACX,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;wBAC5C,OAAO,GAAG,MAAM,CAAC,WAAW,CAC1B,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CACjE,CAAC;oBACJ,CAAC;oBAED,OAAO,IAAI,CAAC,mBAAmB,CAAC;wBAC9B,SAAS,EAAE,OAAO;wBAClB,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM;qBACnC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;gBAEjD,KAAK,KAAK;oBACR,+CAA+C;oBAC/C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;oBAC9B,OAAO,IAAI,CAAC,mBAAmB,CAAC;wBAC9B,QAAQ;wBACR,KAAK;wBACL,GAAG,EAAE,IAAI;wBACT,IAAI,EAAE,uCAAuC;qBAC9C,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;gBAEnD;oBACE,OAAO,IAAI,CAAC,iBAAiB,CAC3B,mBAAmB,MAAM,EAAE,EAC3B,EAAE,MAAM,EAAE,EACV,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;YACN,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,iCAAiC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EACzF,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,EAAE,EAC7B,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;CACF"}