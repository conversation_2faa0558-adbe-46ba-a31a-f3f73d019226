import { nanoid } from 'nanoid';
import { promises as fs } from 'fs';
import * as path from 'path';
import { logger } from '../utils/logger.js';
import { settings } from '../config/settings.js';
class SessionManager {
    sessions = new Map();
    currentSessionId = null;
    sessionsDirectory;
    maxSessions;
    autoSaveInterval = null;
    constructor() {
        this.sessionsDirectory = path.join(process.cwd(), '.ai-cli', 'sessions');
        this.maxSessions = settings.settings.ui.maxHistorySize;
        this.ensureSessionsDirectory();
        this.startAutoSave();
    }
    // Create a new session
    async createSession(name, options = {}) {
        const session = {
            id: nanoid(),
            userId: options.userId,
            name,
            description: options.description,
            createdAt: new Date(),
            lastAccessedAt: new Date(),
            messages: [],
            context: options.context || {},
            metadata: options.metadata || {},
            workflowIds: [],
            totalMessages: 0,
            totalTokensUsed: 0,
        };
        this.sessions.set(session.id, session);
        this.currentSessionId = session.id;
        await this.saveSession(session);
        logger.info(`Created new session: ${session.name} (${session.id})`);
        return session;
    }
    // Get current session or create a new one
    async getCurrentSession() {
        if (this.currentSessionId) {
            const session = this.sessions.get(this.currentSessionId);
            if (session) {
                session.lastAccessedAt = new Date();
                return session;
            }
        }
        // Create default session if none exists
        return this.createSession('Default Session', {
            description: 'Automatically created session',
        });
    }
    // Switch to a different session
    async switchSession(sessionId) {
        let session = this.sessions.get(sessionId);
        if (!session) {
            // Try to load from disk
            session = await this.loadSession(sessionId) || undefined;
        }
        if (session) {
            this.currentSessionId = sessionId;
            session.lastAccessedAt = new Date();
            logger.info(`Switched to session: ${session.name} (${sessionId})`);
            return session;
        }
        return null;
    }
    // Add message to current session
    async addMessage(message, tokensUsed = 0) {
        const session = await this.getCurrentSession();
        message.timestamp = message.timestamp || new Date();
        session.messages.push(message);
        session.totalMessages++;
        session.totalTokensUsed += tokensUsed;
        session.lastAccessedAt = new Date();
        // Limit message history
        const maxMessages = settings.settings.ui.maxHistorySize;
        if (session.messages.length > maxMessages) {
            session.messages = session.messages.slice(-maxMessages);
        }
        await this.saveSession(session);
    }
    // Get session messages with optional filtering
    getSessionMessages(sessionId, options = {}) {
        const session = sessionId
            ? this.sessions.get(sessionId)
            : this.sessions.get(this.currentSessionId);
        if (!session) {
            return [];
        }
        let messages = [...session.messages];
        // Filter by role
        if (options.role) {
            messages = messages.filter(msg => msg.role === options.role);
        }
        // Filter by date
        if (options.fromDate) {
            messages = messages.filter(msg => msg.timestamp && msg.timestamp >= options.fromDate);
        }
        // Limit results
        if (options.limit) {
            messages = messages.slice(-options.limit);
        }
        return messages;
    }
    // Update session context
    async updateSessionContext(sessionId, context) {
        const session = this.sessions.get(sessionId);
        if (session) {
            session.context = { ...session.context, ...context };
            session.lastAccessedAt = new Date();
            await this.saveSession(session);
        }
    }
    // Add workflow to session
    async addWorkflowToSession(sessionId, workflowId) {
        const session = this.sessions.get(sessionId);
        if (session && !session.workflowIds.includes(workflowId)) {
            session.workflowIds.push(workflowId);
            session.lastAccessedAt = new Date();
            await this.saveSession(session);
        }
    }
    // List all sessions
    async listSessions(options = {}) {
        await this.loadAllSessions();
        let sessions = Array.from(this.sessions.values());
        // Apply filters
        if (options.userId) {
            sessions = sessions.filter(s => s.userId === options.userId);
        }
        if (options.dateFrom) {
            sessions = sessions.filter(s => s.createdAt >= options.dateFrom);
        }
        if (options.dateTo) {
            sessions = sessions.filter(s => s.createdAt <= options.dateTo);
        }
        if (options.query) {
            const query = options.query.toLowerCase();
            sessions = sessions.filter(s => s.name.toLowerCase().includes(query) ||
                (s.description && s.description.toLowerCase().includes(query)));
        }
        // Sort sessions
        const sortBy = options.sortBy || 'lastAccessedAt';
        const sortOrder = options.sortOrder || 'desc';
        sessions.sort((a, b) => {
            let aValue, bValue;
            switch (sortBy) {
                case 'messageCount':
                    aValue = a.totalMessages;
                    bValue = b.totalMessages;
                    break;
                case 'createdAt':
                    aValue = a.createdAt;
                    bValue = b.createdAt;
                    break;
                default:
                    aValue = a.lastAccessedAt;
                    bValue = b.lastAccessedAt;
            }
            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            }
            else {
                return aValue < bValue ? 1 : -1;
            }
        });
        // Limit results
        if (options.limit) {
            sessions = sessions.slice(0, options.limit);
        }
        // Convert to summaries
        return sessions.map(session => ({
            id: session.id,
            name: session.name,
            description: session.description,
            createdAt: session.createdAt,
            lastAccessedAt: session.lastAccessedAt,
            messageCount: session.totalMessages,
            workflowCount: session.workflowIds.length,
        }));
    }
    // Delete a session
    async deleteSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return false;
        }
        this.sessions.delete(sessionId);
        // Delete from disk
        try {
            const sessionFile = path.join(this.sessionsDirectory, `${sessionId}.json`);
            await fs.unlink(sessionFile);
        }
        catch (error) {
            logger.warn(`Failed to delete session file: ${sessionId}`, error);
        }
        // Switch to another session if this was current
        if (this.currentSessionId === sessionId) {
            const remainingSessions = Array.from(this.sessions.values());
            if (remainingSessions.length > 0) {
                this.currentSessionId = remainingSessions[0].id;
            }
            else {
                this.currentSessionId = null;
            }
        }
        logger.info(`Deleted session: ${session.name} (${sessionId})`);
        return true;
    }
    // Export session data
    async exportSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session not found: ${sessionId}`);
        }
        return JSON.stringify(session, null, 2);
    }
    // Import session data
    async importSession(sessionData) {
        try {
            const session = JSON.parse(sessionData);
            // Validate session data
            if (!session.id || !session.name || !Array.isArray(session.messages)) {
                throw new Error('Invalid session data format');
            }
            // Generate new ID to avoid conflicts
            session.id = nanoid();
            session.lastAccessedAt = new Date();
            this.sessions.set(session.id, session);
            await this.saveSession(session);
            logger.info(`Imported session: ${session.name} (${session.id})`);
            return session;
        }
        catch (error) {
            throw new Error(`Failed to import session: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    // Get session statistics
    getSessionStats() {
        const sessions = Array.from(this.sessions.values());
        if (sessions.length === 0) {
            return {
                totalSessions: 0,
                totalMessages: 0,
                totalTokensUsed: 0,
                averageMessagesPerSession: 0,
            };
        }
        const totalMessages = sessions.reduce((sum, s) => sum + s.totalMessages, 0);
        const totalTokensUsed = sessions.reduce((sum, s) => sum + s.totalTokensUsed, 0);
        const dates = sessions.map(s => s.createdAt);
        return {
            totalSessions: sessions.length,
            totalMessages,
            totalTokensUsed,
            averageMessagesPerSession: Math.round(totalMessages / sessions.length),
            oldestSession: new Date(Math.min(...dates.map(d => d.getTime()))),
            newestSession: new Date(Math.max(...dates.map(d => d.getTime()))),
        };
    }
    // Clean up old sessions
    async cleanupOldSessions(maxAge = 30 * 24 * 60 * 60 * 1000) {
        const cutoffDate = new Date(Date.now() - maxAge);
        const sessionsToDelete = [];
        for (const [sessionId, session] of this.sessions.entries()) {
            if (session.lastAccessedAt < cutoffDate) {
                sessionsToDelete.push(sessionId);
            }
        }
        let deletedCount = 0;
        for (const sessionId of sessionsToDelete) {
            if (await this.deleteSession(sessionId)) {
                deletedCount++;
            }
        }
        logger.info(`Cleaned up ${deletedCount} old sessions`);
        return deletedCount;
    }
    // Private helper methods
    async ensureSessionsDirectory() {
        try {
            await fs.mkdir(this.sessionsDirectory, { recursive: true });
        }
        catch (error) {
            logger.error('Failed to create sessions directory', error);
        }
    }
    async saveSession(session) {
        try {
            const sessionFile = path.join(this.sessionsDirectory, `${session.id}.json`);
            await fs.writeFile(sessionFile, JSON.stringify(session, null, 2));
        }
        catch (error) {
            logger.error(`Failed to save session: ${session.id}`, error);
        }
    }
    async loadSession(sessionId) {
        try {
            const sessionFile = path.join(this.sessionsDirectory, `${sessionId}.json`);
            const data = await fs.readFile(sessionFile, 'utf8');
            const session = JSON.parse(data);
            // Convert date strings back to Date objects
            session.createdAt = new Date(session.createdAt);
            session.lastAccessedAt = new Date(session.lastAccessedAt);
            session.messages.forEach(msg => {
                if (msg.timestamp) {
                    msg.timestamp = new Date(msg.timestamp);
                }
            });
            this.sessions.set(sessionId, session);
            return session;
        }
        catch (error) {
            logger.warn(`Failed to load session: ${sessionId}`, error);
            return null;
        }
    }
    async loadAllSessions() {
        try {
            const files = await fs.readdir(this.sessionsDirectory);
            const sessionFiles = files.filter(file => file.endsWith('.json'));
            for (const file of sessionFiles) {
                const sessionId = path.basename(file, '.json');
                if (!this.sessions.has(sessionId)) {
                    await this.loadSession(sessionId);
                }
            }
        }
        catch (error) {
            logger.warn('Failed to load sessions from disk', error);
        }
    }
    startAutoSave() {
        if (settings.settings.workflow.autoSave) {
            this.autoSaveInterval = setInterval(async () => {
                for (const session of this.sessions.values()) {
                    await this.saveSession(session);
                }
            }, 60000); // Save every minute
        }
    }
    // Cleanup method
    destroy() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }
}
export const sessionManager = new SessionManager();
export default sessionManager;
//# sourceMappingURL=session-manager.js.map