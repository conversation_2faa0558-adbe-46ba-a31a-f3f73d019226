export interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
    timestamp?: Date;
}
export interface ReasoningStep {
    step: number;
    content: string;
    timestamp: Date;
}
export interface ChatResponse {
    content: string;
    reasoning?: ReasoningStep[];
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
    model: string;
    finish_reason: string;
}
export interface StreamingResponse {
    onContent: (content: string) => void;
    onReasoning: (step: ReasoningStep) => void;
    onComplete: (response: ChatResponse) => void;
    onError: (error: Error) => void;
}
export interface FunctionCall {
    name: string;
    arguments: Record<string, any>;
}
export interface ToolDefinition {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required?: string[];
        };
    };
}
declare class DeepSeekClient {
    private client;
    private baseUrl;
    private apiKey;
    constructor();
    chat(messages: ChatMessage[], options?: {
        model?: string;
        temperature?: number;
        maxTokens?: number;
        tools?: ToolDefinition[];
        stream?: boolean;
    }): Promise<ChatResponse>;
    chatWithReasoning(messages: ChatMessage[], options?: {
        temperature?: number;
        maxTokens?: number;
        tools?: ToolDefinition[];
    }): Promise<ChatResponse>;
    streamChat(messages: ChatMessage[], callbacks: StreamingResponse, options?: {
        model?: string;
        temperature?: number;
        maxTokens?: number;
        tools?: ToolDefinition[];
        useReasoning?: boolean;
    }): void;
    private handleApiError;
    validateApiKey(): Promise<boolean>;
    getModels(): Promise<string[]>;
}
export declare const deepSeekClient: DeepSeekClient;
export default deepSeekClient;
//# sourceMappingURL=ai-client.d.ts.map