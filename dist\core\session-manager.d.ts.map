{"version": 3, "file": "session-manager.d.ts", "sourceRoot": "", "sources": ["../../src/core/session-manager.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,cAAc,EAAE,IAAI,CAAC;IACrB,QAAQ,EAAE,WAAW,EAAE,CAAC;IACxB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9B,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,aAAa,EAAE,MAAM,CAAC;IACtB,eAAe,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,WAAW,cAAc;IAC7B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,cAAc,EAAE,IAAI,CAAC;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,oBAAoB;IACnC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,IAAI,CAAC;IAChB,MAAM,CAAC,EAAE,IAAI,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,WAAW,GAAG,gBAAgB,GAAG,cAAc,CAAC;IACzD,SAAS,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;CAC5B;AAED,cAAM,cAAc;IAClB,OAAO,CAAC,QAAQ,CAAuC;IACvD,OAAO,CAAC,gBAAgB,CAAuB;IAC/C,OAAO,CAAC,iBAAiB,CAAS;IAClC,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,gBAAgB,CAA+B;;IAUjD,aAAa,CACjB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE;QACP,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC9B,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAC3B,GACL,OAAO,CAAC,WAAW,CAAC;IA0BjB,iBAAiB,IAAI,OAAO,CAAC,WAAW,CAAC;IAgBzC,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAmB7D,UAAU,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,GAAE,MAAU,GAAG,OAAO,CAAC,IAAI,CAAC;IAmB7E,kBAAkB,CAChB,SAAS,CAAC,EAAE,MAAM,EAClB,OAAO,GAAE;QACP,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,QAAQ,CAAC,EAAE,IAAI,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,GAAG,WAAW,GAAG,QAAQ,CAAC;KACnC,GACL,WAAW,EAAE;IAgCV,oBAAoB,CACxB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC3B,OAAO,CAAC,IAAI,CAAC;IAUV,oBAAoB,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAU1E,YAAY,CAAC,OAAO,GAAE,oBAAyB,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;IAwE3E,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IA+BlD,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAUjD,aAAa,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC;IAwB9D,eAAe,IAAI;QACjB,aAAa,EAAE,MAAM,CAAC;QACtB,aAAa,EAAE,MAAM,CAAC;QACtB,eAAe,EAAE,MAAM,CAAC;QACxB,yBAAyB,EAAE,MAAM,CAAC;QAClC,aAAa,CAAC,EAAE,IAAI,CAAC;QACrB,aAAa,CAAC,EAAE,IAAI,CAAC;KACtB;IA2BK,kBAAkB,CAAC,MAAM,GAAE,MAAiC,GAAG,OAAO,CAAC,MAAM,CAAC;YAsBtE,uBAAuB;YAQvB,WAAW;YASX,WAAW;YAuBX,eAAe;IAgB7B,OAAO,CAAC,aAAa;IAWrB,OAAO,IAAI,IAAI;CAMhB;AAED,eAAO,MAAM,cAAc,gBAAuB,CAAC;AACnD,eAAe,cAAc,CAAC"}