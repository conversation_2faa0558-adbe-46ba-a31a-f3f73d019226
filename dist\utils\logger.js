import winston from 'winston';
import path from 'path';
import { settings } from '../config/settings.js';
class Logger {
    logger;
    constructor() {
        this.logger = winston.createLogger({
            level: settings.settings.logging.level,
            format: winston.format.combine(winston.format.timestamp({
                format: 'YYYY-MM-DD HH:mm:ss'
            }), winston.format.errors({ stack: true }), winston.format.json()),
            defaultMeta: { service: 'ai-cli-tool' },
            transports: [
                // File transport
                new winston.transports.File({
                    filename: path.join(process.cwd(), settings.settings.logging.file),
                    maxsize: settings.settings.logging.maxSize,
                    maxFiles: settings.settings.logging.maxFiles,
                    tailable: true,
                }),
                // Console transport for development
                new winston.transports.Console({
                    format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
                    silent: settings.settings.ui.verbosity === 'quiet'
                })
            ],
        });
        // Handle uncaught exceptions and rejections
        this.logger.exceptions.handle(new winston.transports.File({
            filename: path.join(process.cwd(), 'exceptions.log')
        }));
        this.logger.rejections.handle(new winston.transports.File({
            filename: path.join(process.cwd(), 'rejections.log')
        }));
    }
    debug(message, meta) {
        this.logger.debug(message, meta);
    }
    info(message, meta) {
        this.logger.info(message, meta);
    }
    warn(message, meta) {
        this.logger.warn(message, meta);
    }
    error(message, error) {
        if (error instanceof Error) {
            this.logger.error(message, {
                error: {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                }
            });
        }
        else {
            this.logger.error(message, { error });
        }
    }
    // Method to log API calls
    logApiCall(method, url, duration, status) {
        this.info('API Call', {
            method,
            url,
            duration,
            status,
            type: 'api_call'
        });
    }
    // Method to log tool executions
    logToolExecution(toolName, parameters, duration, success) {
        this.info('Tool Execution', {
            toolName,
            parameters,
            duration,
            success,
            type: 'tool_execution'
        });
    }
    // Method to log workflow steps
    logWorkflowStep(stepName, stepData) {
        this.info('Workflow Step', {
            stepName,
            stepData,
            type: 'workflow_step'
        });
    }
    // Method to log user interactions
    logUserInteraction(command, parameters) {
        this.info('User Interaction', {
            command,
            parameters,
            type: 'user_interaction'
        });
    }
    // Method to update log level dynamically
    setLevel(level) {
        this.logger.level = level;
        this.logger.transports.forEach(transport => {
            transport.level = level;
        });
    }
    // Method to get current log level
    getLevel() {
        return this.logger.level;
    }
    // Method to create child logger with additional context
    child(meta) {
        return this.logger.child(meta);
    }
}
export const logger = new Logger();
export default logger;
//# sourceMappingURL=logger.js.map