import axios from 'axios';
import { settings } from '../config/settings.js';
import { logger } from '../utils/logger.js';
class DeepSeekClient {
    client;
    baseUrl;
    apiKey;
    constructor() {
        const config = settings.settings.deepseek;
        this.baseUrl = config.baseUrl;
        this.apiKey = config.apiKey;
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: config.timeout,
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
            },
        });
        // Request interceptor for logging
        this.client.interceptors.request.use((config) => {
            logger.debug('DeepSeek API Request', {
                url: config.url,
                method: config.method,
                data: config.data,
            });
            return config;
        }, (error) => {
            logger.error('DeepSeek API Request Error', error);
            return Promise.reject(error);
        });
        // Response interceptor for logging
        this.client.interceptors.response.use((response) => {
            logger.debug('DeepSeek API Response', {
                status: response.status,
                data: response.data,
            });
            return response;
        }, (error) => {
            logger.error('DeepSeek API Response Error', {
                status: error.response?.status,
                data: error.response?.data,
                message: error.message,
            });
            return Promise.reject(error);
        });
    }
    async chat(messages, options = {}) {
        const config = settings.settings.deepseek;
        const startTime = Date.now();
        try {
            const requestData = {
                model: options.model || config.chatModel,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                })),
                temperature: options.temperature ?? config.temperature,
                max_tokens: options.maxTokens || config.maxTokens,
                stream: options.stream || false,
                ...(options.tools && { tools: options.tools }),
            };
            const response = await this.client.post('/chat/completions', requestData);
            const duration = Date.now() - startTime;
            logger.logApiCall('POST', '/chat/completions', duration, response.status);
            const choice = response.data.choices[0];
            return {
                content: choice.message.content,
                usage: response.data.usage,
                model: response.data.model,
                finish_reason: choice.finish_reason,
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger.logApiCall('POST', '/chat/completions', duration, error.response?.status);
            throw this.handleApiError(error);
        }
    }
    async chatWithReasoning(messages, options = {}) {
        const config = settings.settings.deepseek;
        const startTime = Date.now();
        try {
            const requestData = {
                model: config.reasonerModel,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                })),
                temperature: options.temperature ?? config.temperature,
                max_tokens: options.maxTokens || config.maxTokens,
                ...(options.tools && { tools: options.tools }),
            };
            const response = await this.client.post('/chat/completions', requestData);
            const duration = Date.now() - startTime;
            logger.logApiCall('POST', '/chat/completions (reasoning)', duration, response.status);
            const choice = response.data.choices[0];
            // Parse reasoning steps if available
            const reasoning = [];
            if (choice.message.reasoning) {
                choice.message.reasoning.forEach((step, index) => {
                    reasoning.push({
                        step: index + 1,
                        content: step.content,
                        timestamp: new Date(),
                    });
                });
            }
            return {
                content: choice.message.content,
                reasoning,
                usage: response.data.usage,
                model: response.data.model,
                finish_reason: choice.finish_reason,
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            logger.logApiCall('POST', '/chat/completions (reasoning)', duration, error.response?.status);
            throw this.handleApiError(error);
        }
    }
    async streamChat(messages, callbacks, options = {}) {
        try {
            // For now, simulate streaming by calling the regular API and chunking the response
            // In a real implementation, you would use the actual streaming endpoint
            const response = options.useReasoning
                ? await this.chatWithReasoning(messages, options)
                : await this.chat(messages, options);
            // Simulate streaming by sending content in chunks
            const content = response.content;
            const chunkSize = 10;
            let accumulatedContent = '';
            for (let i = 0; i < content.length; i += chunkSize) {
                const chunk = content.slice(i, i + chunkSize);
                accumulatedContent += chunk;
                callbacks.onContent(chunk);
                // Add small delay to simulate streaming
                await new Promise(resolve => setTimeout(resolve, 50));
            }
            // Send reasoning steps if available
            if (response.reasoning) {
                for (const step of response.reasoning) {
                    callbacks.onReasoning(step);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }
            callbacks.onComplete({
                content: accumulatedContent,
                reasoning: response.reasoning,
                model: response.model,
                finish_reason: 'stop',
            });
        }
        catch (error) {
            callbacks.onError(error instanceof Error ? error : new Error(String(error)));
        }
    }
    handleApiError(error) {
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.message;
            switch (status) {
                case 401:
                    return new Error('Invalid API key. Please check your DeepSeek API key.');
                case 429:
                    return new Error('Rate limit exceeded. Please try again later.');
                case 500:
                    return new Error('DeepSeek API server error. Please try again later.');
                default:
                    return new Error(`DeepSeek API error (${status}): ${message}`);
            }
        }
        else if (error.request) {
            return new Error('Network error: Unable to connect to DeepSeek API.');
        }
        else {
            return new Error(`Request error: ${error.message}`);
        }
    }
    // Method to validate API key
    async validateApiKey() {
        try {
            await this.chat([{ role: 'user', content: 'Hello' }], { maxTokens: 1 });
            return true;
        }
        catch (error) {
            return false;
        }
    }
    // Method to get available models
    async getModels() {
        try {
            const response = await this.client.get('/models');
            return response.data.data.map((model) => model.id);
        }
        catch (error) {
            logger.error('Failed to fetch models', error);
            return [settings.settings.deepseek.chatModel, settings.settings.deepseek.reasonerModel];
        }
    }
}
export const deepSeekClient = new DeepSeekClient();
export default deepSeekClient;
//# sourceMappingURL=ai-client.js.map