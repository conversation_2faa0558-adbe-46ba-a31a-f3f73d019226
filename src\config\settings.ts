import Conf from 'conf';
import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration schema
const ConfigSchema = z.object({
  deepseek: z.object({
    apiKey: z.string().min(1, 'DeepSeek API key is required'),
    baseUrl: z.string().url().default('https://api.deepseek.com'),
    chatModel: z.string().default('deepseek-chat'),
    reasonerModel: z.string().default('deepseek-reasoner'),
    maxTokens: z.number().default(4096),
    temperature: z.number().min(0).max(2).default(0.7),
    timeout: z.number().default(30000),
  }),
  ui: z.object({
    theme: z.enum(['dark', 'light']).default('dark'),
    verbosity: z.enum(['quiet', 'normal', 'verbose', 'debug']).default('normal'),
    showReasoningSteps: z.boolean().default(true),
    animationSpeed: z.number().min(50).max(1000).default(200),
    maxHistorySize: z.number().default(100),
  }),
  workflow: z.object({
    maxRetries: z.number().default(3),
    retryDelay: z.number().default(1000),
    sessionTimeout: z.number().default(3600000), // 1 hour
    autoSave: z.boolean().default(true),
  }),
  logging: z.object({
    level: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    file: z.string().default('ai-cli.log'),
    maxSize: z.number().default(10485760), // 10MB
    maxFiles: z.number().default(5),
  }),
});

export type Config = z.infer<typeof ConfigSchema>;

class Settings {
  private config: Conf<Config>;
  private _settings: Config;

  constructor() {
    this.config = new Conf<Config>({
      projectName: 'ai-cli-tool',
      schema: {
        deepseek: {
          type: 'object',
          properties: {
            apiKey: { type: 'string', default: '' },
            baseUrl: { type: 'string', default: 'https://api.deepseek.com' },
            chatModel: { type: 'string', default: 'deepseek-chat' },
            reasonerModel: { type: 'string', default: 'deepseek-reasoner' },
            maxTokens: { type: 'number', default: 4096 },
            temperature: { type: 'number', default: 0.7 },
            timeout: { type: 'number', default: 30000 },
          },
          required: [],
        },
        ui: {
          type: 'object',
          properties: {
            theme: { type: 'string', enum: ['dark', 'light'], default: 'dark' },
            verbosity: { type: 'string', enum: ['quiet', 'normal', 'verbose', 'debug'], default: 'normal' },
            showReasoningSteps: { type: 'boolean', default: true },
            animationSpeed: { type: 'number', default: 200 },
            maxHistorySize: { type: 'number', default: 100 },
          },
          required: [],
        },
        workflow: {
          type: 'object',
          properties: {
            maxRetries: { type: 'number', default: 3 },
            retryDelay: { type: 'number', default: 1000 },
            sessionTimeout: { type: 'number', default: 3600000 },
            autoSave: { type: 'boolean', default: true },
          },
          required: [],
        },
        logging: {
          type: 'object',
          properties: {
            level: { type: 'string', enum: ['error', 'warn', 'info', 'debug'], default: 'info' },
            file: { type: 'string', default: 'ai-cli.log' },
            maxSize: { type: 'number', default: 10485760 },
            maxFiles: { type: 'number', default: 5 },
          },
          required: [],
        },
      },
    });

    this._settings = this.loadSettings();
  }

  private loadSettings(): Config {
    const envSettings = {
      deepseek: {
        apiKey: process.env.DEEPSEEK_API_KEY || this.config.get('deepseek.apiKey', ''),
        baseUrl: process.env.DEEPSEEK_BASE_URL || this.config.get('deepseek.baseUrl', 'https://api.deepseek.com'),
        chatModel: process.env.DEEPSEEK_CHAT_MODEL || this.config.get('deepseek.chatModel', 'deepseek-chat'),
        reasonerModel: process.env.DEEPSEEK_REASONER_MODEL || this.config.get('deepseek.reasonerModel', 'deepseek-reasoner'),
        maxTokens: parseInt(process.env.DEEPSEEK_MAX_TOKENS || '') || this.config.get('deepseek.maxTokens', 4096),
        temperature: parseFloat(process.env.DEEPSEEK_TEMPERATURE || '') || this.config.get('deepseek.temperature', 0.7),
        timeout: parseInt(process.env.DEEPSEEK_TIMEOUT || '') || this.config.get('deepseek.timeout', 30000),
      },
      ui: {
        theme: (process.env.UI_THEME as 'dark' | 'light') || this.config.get('ui.theme', 'dark'),
        verbosity: (process.env.UI_VERBOSITY as any) || this.config.get('ui.verbosity', 'normal'),
        showReasoningSteps: process.env.UI_SHOW_REASONING === 'true' || this.config.get('ui.showReasoningSteps', true),
        animationSpeed: parseInt(process.env.UI_ANIMATION_SPEED || '') || this.config.get('ui.animationSpeed', 200),
        maxHistorySize: parseInt(process.env.UI_MAX_HISTORY || '') || this.config.get('ui.maxHistorySize', 100),
      },
      workflow: {
        maxRetries: parseInt(process.env.WORKFLOW_MAX_RETRIES || '') || this.config.get('workflow.maxRetries', 3),
        retryDelay: parseInt(process.env.WORKFLOW_RETRY_DELAY || '') || this.config.get('workflow.retryDelay', 1000),
        sessionTimeout: parseInt(process.env.WORKFLOW_SESSION_TIMEOUT || '') || this.config.get('workflow.sessionTimeout', 3600000),
        autoSave: process.env.WORKFLOW_AUTO_SAVE !== 'false' && this.config.get('workflow.autoSave', true),
      },
      logging: {
        level: (process.env.LOG_LEVEL as any) || this.config.get('logging.level', 'info'),
        file: process.env.LOG_FILE || this.config.get('logging.file', 'ai-cli.log'),
        maxSize: parseInt(process.env.LOG_MAX_SIZE || '') || this.config.get('logging.maxSize', 10485760),
        maxFiles: parseInt(process.env.LOG_MAX_FILES || '') || this.config.get('logging.maxFiles', 5),
      },
    };

    return ConfigSchema.parse(envSettings);
  }

  get settings(): Config {
    return this._settings;
  }

  updateSetting<K extends keyof Config>(section: K, key: keyof Config[K], value: Config[K][keyof Config[K]]): void {
    this.config.set(`${section}.${String(key)}` as any, value);
    this._settings = this.loadSettings();
  }

  resetToDefaults(): void {
    this.config.clear();
    this._settings = this.loadSettings();
  }

  validateApiKey(): boolean {
    return this._settings.deepseek.apiKey.length > 0;
  }

  export(): Config {
    return { ...this._settings };
  }

  import(settings: Partial<Config>): void {
    Object.entries(settings).forEach(([section, sectionSettings]) => {
      if (sectionSettings && typeof sectionSettings === 'object') {
        Object.entries(sectionSettings).forEach(([key, value]) => {
          this.config.set(`${section}.${key}` as any, value);
        });
      }
    });
    this._settings = this.loadSettings();
  }
}

export const settings = new Settings();
export default settings;
