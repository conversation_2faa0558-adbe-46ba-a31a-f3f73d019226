import { z } from 'zod';
import { logger } from '../utils/logger.js';
export class BaseTool {
    // Get tool definition for AI model
    getDefinition() {
        return {
            name: this.name,
            description: this.description,
            parameters: this.parameters,
            examples: this.examples,
        };
    }
    // Validate parameters before execution
    validateParameters(parameters) {
        try {
            const schema = this.getParameterSchema();
            const validatedData = schema.parse(parameters);
            return { valid: true, data: validatedData };
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
                return { valid: false, error: `Parameter validation failed: ${errorMessages.join(', ')}` };
            }
            return { valid: false, error: `Parameter validation failed: ${error instanceof Error ? error.message : String(error)}` };
        }
    }
    // Execute with validation and error handling
    async safeExecute(parameters, context) {
        const startTime = Date.now();
        try {
            // Validate parameters
            const validation = this.validateParameters(parameters);
            if (!validation.valid) {
                return {
                    success: false,
                    error: validation.error,
                    duration: Date.now() - startTime,
                };
            }
            logger.debug(`Executing tool: ${this.name}`, { parameters, context });
            // Execute the tool
            const result = await this.execute(validation.data, context);
            logger.logToolExecution(this.name, parameters, result.duration, result.success);
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error(`Tool execution failed: ${this.name}`, error);
            logger.logToolExecution(this.name, parameters, duration, false);
            return {
                success: false,
                error: errorMessage,
                duration,
            };
        }
    }
    // Helper method to create successful result
    createSuccessResult(result, metadata, duration) {
        return {
            success: true,
            result,
            metadata,
            duration: duration || 0,
        };
    }
    // Helper method to create error result
    createErrorResult(error, metadata, duration) {
        return {
            success: false,
            error,
            metadata,
            duration: duration || 0,
        };
    }
    // Method to check if tool can be executed in current context
    canExecute(context) {
        return true; // Override in subclasses if needed
    }
    // Method to get tool usage statistics
    getUsageStats() {
        return {
            name: this.name,
            description: this.description,
            parameterCount: Object.keys(this.parameters.properties).length,
            requiredParameterCount: this.parameters.required?.length || 0,
            exampleCount: this.examples?.length || 0,
        };
    }
    // Method to generate parameter documentation
    generateParameterDocs() {
        const docs = [];
        docs.push(`## ${this.name}`);
        docs.push(`${this.description}\n`);
        docs.push('### Parameters:');
        Object.entries(this.parameters.properties).forEach(([name, param]) => {
            const required = this.parameters.required?.includes(name) ? ' (required)' : ' (optional)';
            docs.push(`- **${name}**${required}: ${param.description}`);
            if (param.type)
                docs.push(`  - Type: ${param.type}`);
            if (param.enum)
                docs.push(`  - Allowed values: ${param.enum.join(', ')}`);
        });
        if (this.examples && this.examples.length > 0) {
            docs.push('\n### Examples:');
            this.examples.forEach((example, index) => {
                docs.push(`\n#### Example ${index + 1}: ${example.description}`);
                docs.push('```json');
                docs.push(JSON.stringify(example.input, null, 2));
                docs.push('```');
                docs.push(`Expected output: ${example.expectedOutput}`);
            });
        }
        return docs.join('\n');
    }
}
// Utility function to create parameter definitions
export function createParameter(type, description, options = {}) {
    return {
        type,
        description,
        ...options,
    };
}
// Utility function to create tool examples
export function createExample(description, input, expectedOutput) {
    return {
        description,
        input,
        expectedOutput,
    };
}
//# sourceMappingURL=base-tool.js.map