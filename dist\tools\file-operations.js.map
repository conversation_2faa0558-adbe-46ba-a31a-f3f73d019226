{"version": 3, "file": "file-operations.js", "sourceRoot": "", "sources": ["../../src/tools/file-operations.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,IAAI,EAAE,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,QAAQ,EAA6C,eAAe,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAErH,iBAAiB;AACjB,MAAM,OAAO,YAAa,SAAQ,QAAQ;IAC/B,IAAI,GAAG,WAAW,CAAC;IACnB,WAAW,GAAG,iDAAiD,CAAC;IAChE,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,iDAAiD,CAAC;YAClF,QAAQ,EAAE,eAAe,CAAC,QAAQ,EAAE,+BAA+B,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;SACnH;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,kBAAkB,EAClB,EAAE,IAAI,EAAE,eAAe,EAAE,EACzB,iDAAiD,CAClD;QACD,aAAa,CACX,8BAA8B,EAC9B,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAC3C,kDAAkD,CACnD;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;SACrE,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,UAAU,CAAC;YAChD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAEtE,2DAA2D;YAC3D,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,kDAAkD,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAChH,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAC1D,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1C,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE;gBACvC,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,QAAQ;gBACR,YAAY,EAAE,KAAK,CAAC,KAAK;aAC1B,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAChF,EAAE,EACF,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAED,kBAAkB;AAClB,MAAM,OAAO,aAAc,SAAQ,QAAQ;IAChC,IAAI,GAAG,YAAY,CAAC;IACpB,WAAW,GAAG,2CAA2C,CAAC;IAC1D,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,kDAAkD,CAAC;YACnF,OAAO,EAAE,eAAe,CAAC,QAAQ,EAAE,8BAA8B,CAAC;YAClE,QAAQ,EAAE,eAAe,CAAC,QAAQ,EAAE,+BAA+B,EAAE,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;YAClH,iBAAiB,EAAE,eAAe,CAAC,SAAS,EAAE,iEAAiE,CAAC;SACjH;QACD,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;KAC9B,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,sBAAsB,EACtB,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,EAClD,6DAA6D,CAC9D;QACD,aAAa,CACX,yCAAyC,EACzC,EAAE,IAAI,EAAE,oBAAoB,EAAE,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,IAAI,EAAE,EACpF,kEAAkE,CACnE;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE;YACnB,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YACpE,iBAAiB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;SAC9C,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,UAAU,CAAC;YAC5E,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAEtE,iBAAiB;YACjB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,kDAAkD,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAChH,CAAC;YAED,kCAAkC;YAClC,IAAI,iBAAiB,EAAE,CAAC;gBACtB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBACvC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC3C,CAAC;YAED,MAAM,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACpD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1C,OAAO,IAAI,CAAC,mBAAmB,CAAC,2BAA2B,EAAE;gBAC3D,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,QAAQ;gBACR,OAAO,EAAE,KAAK,CAAC,SAAS;gBACxB,QAAQ,EAAE,KAAK,CAAC,KAAK;aACtB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EACjF,EAAE,EACF,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAED,sBAAsB;AACtB,MAAM,OAAO,iBAAkB,SAAQ,QAAQ;IACpC,IAAI,GAAG,gBAAgB,CAAC;IACxB,WAAW,GAAG,gDAAgD,CAAC;IAC/D,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,4DAA4D,CAAC;YAC7F,SAAS,EAAE,eAAe,CAAC,SAAS,EAAE,yCAAyC,CAAC;YAChF,aAAa,EAAE,eAAe,CAAC,SAAS,EAAE,uDAAuD,CAAC;YAClG,OAAO,EAAE,eAAe,CAAC,QAAQ,EAAE,yCAAyC,CAAC;SAC9E;QACD,QAAQ,EAAE,EAAE;KACb,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,wBAAwB,EACxB,EAAE,IAAI,EAAE,GAAG,EAAE,EACb,wDAAwD,CACzD;QACD,aAAa,CACX,mCAAmC,EACnC,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,EACnD,kEAAkE,CACnE;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;YAC7B,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YACrC,aAAa,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YACzC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;SAC/B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC;YACxE,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;YAErE,iBAAiB;YACjB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,kDAAkD,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAChH,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAExF,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE;gBACrC,IAAI,EAAE,YAAY;gBAClB,KAAK,EAAE,KAAK,CAAC,MAAM;gBACnB,SAAS;gBACT,aAAa;gBACb,OAAO;aACR,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EACrF,EAAE,EACF,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,OAAe,EACf,SAAkB,EAClB,aAAsB,EACtB,OAAgB;QAEhB,MAAM,KAAK,GAAsG,EAAE,CAAC;QAEpH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;QAEnE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC5B,qCAAqC;YACrC,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjD,SAAS;YACX,CAAC;YAED,oCAAoC;YACpC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;gBACzD,SAAS;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEtC,MAAM,IAAI,GAAG;gBACX,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAoB,CAAC,CAAC,CAAC,MAAe;gBAClE,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBAC7C,QAAQ,EAAE,KAAK,CAAC,KAAK;aACtB,CAAC;YAEF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjB,2CAA2C;YAC3C,IAAI,SAAS,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;gBACvF,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,cAAc,CAAC,QAAgB,EAAE,OAAe;QACtD,iDAAiD;QACjD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3E,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;CACF;AAED,mBAAmB;AACnB,MAAM,OAAO,cAAe,SAAQ,QAAQ;IACjC,IAAI,GAAG,aAAa,CAAC;IACrB,WAAW,GAAG,gDAAgD,CAAC;IAC/D,UAAU,GAAG;QACpB,IAAI,EAAE,QAAiB;QACvB,UAAU,EAAE;YACV,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,yCAAyC,CAAC;YAC1E,SAAS,EAAE,eAAe,CAAC,SAAS,EAAE,iDAAiD,CAAC;YACxF,KAAK,EAAE,eAAe,CAAC,SAAS,EAAE,sDAAsD,CAAC;SAC1F;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB,CAAC;IACO,QAAQ,GAAG;QAClB,aAAa,CACX,eAAe,EACf,EAAE,IAAI,EAAE,YAAY,EAAE,EACtB,4BAA4B,CAC7B;QACD,aAAa,CACX,gCAAgC,EAChC,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EACpD,4CAA4C,CAC7C;KACF,CAAC;IAEQ,kBAAkB;QAC1B,OAAO,CAAC,CAAC,MAAM,CAAC;YACd,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YACrC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;SAClC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,UAAe,EAAE,OAA6B;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC;YACxD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAEtE,iBAAiB;YACjB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,iBAAiB,CAAC,kDAAkD,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;YAChH,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1C,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxB,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,gDAAgD,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;gBAC9G,CAAC;gBACD,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAChC,CAAC;YAED,OAAO,IAAI,CAAC,mBAAmB,CAAC,qCAAqC,EAAE;gBACrE,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;gBAChD,SAAS;gBACT,KAAK;aACN,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,iBAAiB,CAC3B,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAC7E,EAAE,EACF,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CACvB,CAAC;QACJ,CAAC;IACH,CAAC;CACF"}