import { z } from 'zod';
declare const ConfigSchema: z.ZodObject<{
    deepseek: z.ZodObject<{
        apiKey: z.ZodString;
        baseUrl: z.ZodDefault<z.ZodString>;
        chatModel: z.<PERSON>od<PERSON><PERSON>ault<z.ZodString>;
        reasonerModel: z.<PERSON><PERSON><PERSON><z.ZodString>;
        maxTokens: z.<PERSON><z.ZodNumber>;
        temperature: z.ZodDefault<z.ZodNumber>;
        timeout: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        apiKey: string;
        baseUrl: string;
        chatModel: string;
        reasonerModel: string;
        maxTokens: number;
        temperature: number;
        timeout: number;
    }, {
        apiKey: string;
        baseUrl?: string | undefined;
        chatModel?: string | undefined;
        reasonerModel?: string | undefined;
        maxTokens?: number | undefined;
        temperature?: number | undefined;
        timeout?: number | undefined;
    }>;
    ui: z.ZodObject<{
        theme: z.<PERSON>od<PERSON><PERSON>ault<z.<PERSON>od<PERSON>num<["dark", "light"]>>;
        verbosity: z.<PERSON><PERSON><PERSON><z.ZodEnum<["quiet", "normal", "verbose", "debug"]>>;
        showReasoningSteps: z.ZodDefault<z.ZodBoolean>;
        animationSpeed: z.ZodDefault<z.ZodNumber>;
        maxHistorySize: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        theme: "dark" | "light";
        verbosity: "quiet" | "normal" | "verbose" | "debug";
        showReasoningSteps: boolean;
        animationSpeed: number;
        maxHistorySize: number;
    }, {
        theme?: "dark" | "light" | undefined;
        verbosity?: "quiet" | "normal" | "verbose" | "debug" | undefined;
        showReasoningSteps?: boolean | undefined;
        animationSpeed?: number | undefined;
        maxHistorySize?: number | undefined;
    }>;
    workflow: z.ZodObject<{
        maxRetries: z.ZodDefault<z.ZodNumber>;
        retryDelay: z.ZodDefault<z.ZodNumber>;
        sessionTimeout: z.ZodDefault<z.ZodNumber>;
        autoSave: z.ZodDefault<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        maxRetries: number;
        retryDelay: number;
        sessionTimeout: number;
        autoSave: boolean;
    }, {
        maxRetries?: number | undefined;
        retryDelay?: number | undefined;
        sessionTimeout?: number | undefined;
        autoSave?: boolean | undefined;
    }>;
    logging: z.ZodObject<{
        level: z.ZodDefault<z.ZodEnum<["error", "warn", "info", "debug"]>>;
        file: z.ZodDefault<z.ZodString>;
        maxSize: z.ZodDefault<z.ZodNumber>;
        maxFiles: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        level: "debug" | "error" | "warn" | "info";
        file: string;
        maxSize: number;
        maxFiles: number;
    }, {
        level?: "debug" | "error" | "warn" | "info" | undefined;
        file?: string | undefined;
        maxSize?: number | undefined;
        maxFiles?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    deepseek: {
        apiKey: string;
        baseUrl: string;
        chatModel: string;
        reasonerModel: string;
        maxTokens: number;
        temperature: number;
        timeout: number;
    };
    ui: {
        theme: "dark" | "light";
        verbosity: "quiet" | "normal" | "verbose" | "debug";
        showReasoningSteps: boolean;
        animationSpeed: number;
        maxHistorySize: number;
    };
    workflow: {
        maxRetries: number;
        retryDelay: number;
        sessionTimeout: number;
        autoSave: boolean;
    };
    logging: {
        level: "debug" | "error" | "warn" | "info";
        file: string;
        maxSize: number;
        maxFiles: number;
    };
}, {
    deepseek: {
        apiKey: string;
        baseUrl?: string | undefined;
        chatModel?: string | undefined;
        reasonerModel?: string | undefined;
        maxTokens?: number | undefined;
        temperature?: number | undefined;
        timeout?: number | undefined;
    };
    ui: {
        theme?: "dark" | "light" | undefined;
        verbosity?: "quiet" | "normal" | "verbose" | "debug" | undefined;
        showReasoningSteps?: boolean | undefined;
        animationSpeed?: number | undefined;
        maxHistorySize?: number | undefined;
    };
    workflow: {
        maxRetries?: number | undefined;
        retryDelay?: number | undefined;
        sessionTimeout?: number | undefined;
        autoSave?: boolean | undefined;
    };
    logging: {
        level?: "debug" | "error" | "warn" | "info" | undefined;
        file?: string | undefined;
        maxSize?: number | undefined;
        maxFiles?: number | undefined;
    };
}>;
export type Config = z.infer<typeof ConfigSchema>;
declare class Settings {
    private config;
    private _settings;
    constructor();
    private loadSettings;
    get settings(): Config;
    updateSetting<K extends keyof Config>(section: K, key: keyof Config[K], value: Config[K][keyof Config[K]]): void;
    resetToDefaults(): void;
    validateApiKey(): boolean;
    export(): Config;
    import(settings: Partial<Config>): void;
}
export declare const settings: Settings;
export default settings;
//# sourceMappingURL=settings.d.ts.map