{"version": 3, "file": "ai-client.js", "sourceRoot": "", "sources": ["../../src/core/ai-client.ts"], "names": [], "mappings": "AAAA,OAAO,KAAuC,MAAM,OAAO,CAAC;AAC5D,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAmD5C,MAAM,cAAc;IACV,MAAM,CAAgB;IACtB,OAAO,CAAS;IAChB,MAAM,CAAS;IAEvB;QACE,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC1C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;gBACxC,cAAc,EAAE,kBAAkB;aACnC;SACF,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE;YACT,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACnC,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,QAAQ,EAAE,EAAE;YACX,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACpC,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,IAAI,EAAE,QAAQ,CAAC,IAAI;aACpB,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC1C,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAuB,EACvB,UAMI,EAAE;QAEN,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS;gBACxC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC,CAAC;gBACH,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW;gBACtD,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS;gBACjD,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK;gBAC/B,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;aAC/C,CAAC;YAEF,MAAM,QAAQ,GAAkB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;YACzF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,mBAAmB,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE1E,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;gBAC1B,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;gBAC1B,aAAa,EAAE,MAAM,CAAC,aAAa;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,mBAAmB,EAAE,QAAQ,EAAG,KAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1F,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,QAAuB,EACvB,UAII,EAAE;QAEN,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,KAAK,EAAE,MAAM,CAAC,aAAa;gBAC3B,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC,CAAC;gBACH,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW;gBACtD,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS;gBACjD,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;aAC/C,CAAC;YAEF,MAAM,QAAQ,GAAkB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;YACzF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,+BAA+B,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEtF,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAExC,qCAAqC;YACrC,MAAM,SAAS,GAAoB,EAAE,CAAC;YACtC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC7B,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,KAAa,EAAE,EAAE;oBAC5D,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,KAAK,GAAG,CAAC;wBACf,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,SAAS;gBACT,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;gBAC1B,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;gBAC1B,aAAa,EAAE,MAAM,CAAC,aAAa;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,+BAA+B,EAAE,QAAQ,EAAG,KAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACtG,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CACd,QAAuB,EACvB,SAA4B,EAC5B,UAMI,EAAE;QAEN,IAAI,CAAC;YACH,mFAAmF;YACnF,wEAAwE;YACxE,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY;gBACnC,CAAC,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC;gBACjD,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAEvC,kDAAkD;YAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YACjC,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,IAAI,kBAAkB,GAAG,EAAE,CAAC;YAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;gBACnD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;gBAC9C,kBAAkB,IAAI,KAAK,CAAC;gBAC5B,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAE3B,wCAAwC;gBACxC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YACxD,CAAC;YAED,oCAAoC;YACpC,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACvB,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;oBACtC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBAC5B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,SAAS,CAAC,UAAU,CAAC;gBACnB,OAAO,EAAE,kBAAkB;gBAC3B,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,aAAa,EAAE,MAAM;aACtB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,KAAU;QAC/B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;YACrC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;YAErE,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;gBAC3E,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;gBACnE,KAAK,GAAG;oBACN,OAAO,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;gBACzE;oBACE,OAAO,IAAI,KAAK,CAAC,uBAAuB,MAAM,MAAM,OAAO,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,KAAK,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,6BAA6B;IAC7B,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;YACxE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,iCAAiC;IACjC,KAAK,CAAC,SAAS;QACb,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC1F,CAAC;IACH,CAAC;CACF;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;AACnD,eAAe,cAAc,CAAC"}