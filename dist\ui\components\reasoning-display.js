import chalk from 'chalk';
import boxen from 'boxen';
export class ReasoningDisplay {
    options;
    steps = [];
    isDisplaying = false;
    constructor(options = {}) {
        this.options = {
            showTimestamps: true,
            showStepNumbers: true,
            maxWidth: 80,
            theme: 'dark',
            realTime: true,
            compact: false,
            ...options,
        };
    }
    // Add a reasoning step and optionally display it immediately
    addStep(step) {
        this.steps.push(step);
        if (this.options.realTime && !this.isDisplaying) {
            this.displayStep(step);
        }
    }
    // Display a single reasoning step
    displayStep(step) {
        const formattedStep = this.formatStep(step);
        console.log(formattedStep);
    }
    // Display all reasoning steps
    displayAllSteps() {
        if (this.steps.length === 0) {
            return;
        }
        this.isDisplaying = true;
        console.log(this.getHeader());
        for (const step of this.steps) {
            console.log(this.formatStep(step));
            if (!this.options.compact) {
                console.log(); // Add spacing between steps
            }
        }
        console.log(this.getFooter());
        this.isDisplaying = false;
    }
    // Format a single reasoning step
    formatStep(step) {
        const theme = this.getTheme();
        let output = '';
        // Step number
        if (this.options.showStepNumbers) {
            const stepNumber = chalk[theme.stepNumber](`[Step ${step.step}]`);
            output += stepNumber + ' ';
        }
        // Timestamp
        if (this.options.showTimestamps) {
            const timestamp = chalk[theme.timestamp](`(${step.timestamp.toLocaleTimeString()})`);
            output += timestamp + ' ';
        }
        // Content
        const content = this.wrapText(step.content, this.options.maxWidth - 20);
        const formattedContent = chalk[theme.content](content);
        if (this.options.compact) {
            output += formattedContent;
        }
        else {
            output += '\n' + this.indentText(formattedContent, 2);
        }
        return output;
    }
    // Get theme colors based on current theme
    getTheme() {
        if (this.options.theme === 'light') {
            return {
                stepNumber: 'blue',
                timestamp: 'gray',
                content: 'black',
                border: 'gray',
                header: 'blue',
                footer: 'blue',
            };
        }
        else {
            return {
                stepNumber: 'cyan',
                timestamp: 'gray',
                content: 'white',
                border: 'gray',
                header: 'cyan',
                footer: 'cyan',
            };
        }
    }
    // Get header for reasoning display
    getHeader() {
        const theme = this.getTheme();
        const title = chalk[theme.header].bold('🧠 AI Reasoning Process');
        if (this.options.compact) {
            return title;
        }
        return boxen(title, {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: theme.border,
            align: 'center',
        });
    }
    // Get footer for reasoning display
    getFooter() {
        const theme = this.getTheme();
        const summary = chalk[theme.footer](`✓ Reasoning complete (${this.steps.length} steps)`);
        if (this.options.compact) {
            return summary;
        }
        return boxen(summary, {
            padding: 0,
            margin: { top: 1, bottom: 1, left: 1, right: 1 },
            borderStyle: 'single',
            borderColor: theme.border,
            align: 'center',
        });
    }
    // Wrap text to specified width
    wrapText(text, width) {
        if (text.length <= width) {
            return text;
        }
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';
        for (const word of words) {
            if ((currentLine + word).length <= width) {
                currentLine += (currentLine ? ' ' : '') + word;
            }
            else {
                if (currentLine) {
                    lines.push(currentLine);
                }
                currentLine = word;
            }
        }
        if (currentLine) {
            lines.push(currentLine);
        }
        return lines.join('\n');
    }
    // Indent text by specified number of spaces
    indentText(text, spaces) {
        const indent = ' '.repeat(spaces);
        return text.split('\n').map(line => indent + line).join('\n');
    }
    // Clear all steps
    clear() {
        this.steps = [];
    }
    // Get step count
    getStepCount() {
        return this.steps.length;
    }
    // Get all steps
    getSteps() {
        return [...this.steps];
    }
    // Export steps as formatted text
    exportAsText() {
        let output = 'AI Reasoning Process\n';
        output += '='.repeat(50) + '\n\n';
        for (const step of this.steps) {
            output += `Step ${step.step} (${step.timestamp.toISOString()}):\n`;
            output += step.content + '\n\n';
        }
        return output;
    }
    // Export steps as JSON
    exportAsJSON() {
        return JSON.stringify({
            timestamp: new Date().toISOString(),
            stepCount: this.steps.length,
            steps: this.steps,
        }, null, 2);
    }
    // Stream reasoning steps in real-time
    startStreaming() {
        this.options.realTime = true;
        console.log(this.getHeader());
    }
    // Stop streaming and show summary
    stopStreaming() {
        this.options.realTime = false;
        if (this.steps.length > 0) {
            console.log(this.getFooter());
        }
    }
    // Update display options
    updateOptions(options) {
        this.options = { ...this.options, ...options };
    }
}
// Utility function to create a reasoning display with common presets
export function createReasoningDisplay(preset) {
    switch (preset) {
        case 'verbose':
            return new ReasoningDisplay({
                showTimestamps: true,
                showStepNumbers: true,
                maxWidth: 100,
                realTime: true,
                compact: false,
            });
        case 'compact':
            return new ReasoningDisplay({
                showTimestamps: false,
                showStepNumbers: true,
                maxWidth: 80,
                realTime: true,
                compact: true,
            });
        case 'minimal':
            return new ReasoningDisplay({
                showTimestamps: false,
                showStepNumbers: false,
                maxWidth: 60,
                realTime: false,
                compact: true,
            });
        default:
            return new ReasoningDisplay();
    }
}
//# sourceMappingURL=reasoning-display.js.map