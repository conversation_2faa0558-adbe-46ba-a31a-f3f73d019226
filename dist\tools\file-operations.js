import { promises as fs } from 'fs';
import * as path from 'path';
import { z } from 'zod';
import { BaseTool, createParameter, createExample } from './base-tool.js';
// File Read Tool
export class FileReadTool extends BaseTool {
    name = 'file_read';
    description = 'Read the contents of a file from the filesystem';
    parameters = {
        type: 'object',
        properties: {
            path: createParameter('string', 'Path to the file to read (relative or absolute)'),
            encoding: createParameter('string', 'File encoding (default: utf8)', { enum: ['utf8', 'ascii', 'base64', 'hex'] }),
        },
        required: ['path'],
    };
    examples = [
        createExample('Read a text file', { path: './config.json' }, 'Returns the contents of config.json as a string'),
        createExample('Read a binary file as base64', { path: './image.png', encoding: 'base64' }, 'Returns the base64 encoded contents of the image'),
    ];
    getParameterSchema() {
        return z.object({
            path: z.string().min(1),
            encoding: z.enum(['utf8', 'ascii', 'base64', 'hex']).default('utf8'),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { path: filePath, encoding } = parameters;
            const resolvedPath = path.resolve(context.workingDirectory, filePath);
            // Security check - ensure path is within working directory
            if (!resolvedPath.startsWith(context.workingDirectory)) {
                return this.createErrorResult('Access denied: Path is outside working directory', {}, Date.now() - startTime);
            }
            const content = await fs.readFile(resolvedPath, encoding);
            const stats = await fs.stat(resolvedPath);
            return this.createSuccessResult(content, {
                path: resolvedPath,
                size: stats.size,
                encoding,
                lastModified: stats.mtime,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`Failed to read file: ${error instanceof Error ? error.message : String(error)}`, {}, Date.now() - startTime);
        }
    }
}
// File Write Tool
export class FileWriteTool extends BaseTool {
    name = 'file_write';
    description = 'Write content to a file on the filesystem';
    parameters = {
        type: 'object',
        properties: {
            path: createParameter('string', 'Path to the file to write (relative or absolute)'),
            content: createParameter('string', 'Content to write to the file'),
            encoding: createParameter('string', 'File encoding (default: utf8)', { enum: ['utf8', 'ascii', 'base64', 'hex'] }),
            createDirectories: createParameter('boolean', 'Create parent directories if they don\'t exist (default: false)'),
        },
        required: ['path', 'content'],
    };
    examples = [
        createExample('Write text to a file', { path: './output.txt', content: 'Hello, World!' }, 'Creates or overwrites output.txt with the specified content'),
        createExample('Write JSON data with directory creation', { path: './data/config.json', content: '{"key": "value"}', createDirectories: true }, 'Creates the data directory if needed and writes the JSON content'),
    ];
    getParameterSchema() {
        return z.object({
            path: z.string().min(1),
            content: z.string(),
            encoding: z.enum(['utf8', 'ascii', 'base64', 'hex']).default('utf8'),
            createDirectories: z.boolean().default(false),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { path: filePath, content, encoding, createDirectories } = parameters;
            const resolvedPath = path.resolve(context.workingDirectory, filePath);
            // Security check
            if (!resolvedPath.startsWith(context.workingDirectory)) {
                return this.createErrorResult('Access denied: Path is outside working directory', {}, Date.now() - startTime);
            }
            // Create directories if requested
            if (createDirectories) {
                const dir = path.dirname(resolvedPath);
                await fs.mkdir(dir, { recursive: true });
            }
            await fs.writeFile(resolvedPath, content, encoding);
            const stats = await fs.stat(resolvedPath);
            return this.createSuccessResult('File written successfully', {
                path: resolvedPath,
                size: stats.size,
                encoding,
                created: stats.birthtime,
                modified: stats.mtime,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`Failed to write file: ${error instanceof Error ? error.message : String(error)}`, {}, Date.now() - startTime);
        }
    }
}
// Directory List Tool
export class DirectoryListTool extends BaseTool {
    name = 'directory_list';
    description = 'List files and directories in a specified path';
    parameters = {
        type: 'object',
        properties: {
            path: createParameter('string', 'Path to the directory to list (default: current directory)'),
            recursive: createParameter('boolean', 'List files recursively (default: false)'),
            includeHidden: createParameter('boolean', 'Include hidden files and directories (default: false)'),
            pattern: createParameter('string', 'Glob pattern to filter files (optional)'),
        },
        required: [],
    };
    examples = [
        createExample('List current directory', { path: '.' }, 'Returns array of files and directories in current path'),
        createExample('List JavaScript files recursively', { path: './src', recursive: true, pattern: '*.js' }, 'Returns all JavaScript files in src directory and subdirectories'),
    ];
    getParameterSchema() {
        return z.object({
            path: z.string().default('.'),
            recursive: z.boolean().default(false),
            includeHidden: z.boolean().default(false),
            pattern: z.string().optional(),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { path: dirPath, recursive, includeHidden, pattern } = parameters;
            const resolvedPath = path.resolve(context.workingDirectory, dirPath);
            // Security check
            if (!resolvedPath.startsWith(context.workingDirectory)) {
                return this.createErrorResult('Access denied: Path is outside working directory', {}, Date.now() - startTime);
            }
            const files = await this.listDirectory(resolvedPath, recursive, includeHidden, pattern);
            return this.createSuccessResult(files, {
                path: resolvedPath,
                count: files.length,
                recursive,
                includeHidden,
                pattern,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`Failed to list directory: ${error instanceof Error ? error.message : String(error)}`, {}, Date.now() - startTime);
        }
    }
    async listDirectory(dirPath, recursive, includeHidden, pattern) {
        const items = [];
        const entries = await fs.readdir(dirPath, { withFileTypes: true });
        for (const entry of entries) {
            // Skip hidden files if not requested
            if (!includeHidden && entry.name.startsWith('.')) {
                continue;
            }
            // Apply pattern filter if specified
            if (pattern && !this.matchesPattern(entry.name, pattern)) {
                continue;
            }
            const fullPath = path.join(dirPath, entry.name);
            const stats = await fs.stat(fullPath);
            const item = {
                name: entry.name,
                path: fullPath,
                type: entry.isDirectory() ? 'directory' : 'file',
                size: entry.isFile() ? stats.size : undefined,
                modified: stats.mtime,
            };
            items.push(item);
            // Recurse into subdirectories if requested
            if (recursive && entry.isDirectory()) {
                const subItems = await this.listDirectory(fullPath, recursive, includeHidden, pattern);
                items.push(...subItems);
            }
        }
        return items;
    }
    matchesPattern(filename, pattern) {
        // Simple glob pattern matching (can be enhanced)
        const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
        return regex.test(filename);
    }
}
// File Delete Tool
export class FileDeleteTool extends BaseTool {
    name = 'file_delete';
    description = 'Delete a file or directory from the filesystem';
    parameters = {
        type: 'object',
        properties: {
            path: createParameter('string', 'Path to the file or directory to delete'),
            recursive: createParameter('boolean', 'Delete directories recursively (default: false)'),
            force: createParameter('boolean', 'Force deletion without confirmation (default: false)'),
        },
        required: ['path'],
    };
    examples = [
        createExample('Delete a file', { path: './temp.txt' }, 'Deletes the specified file'),
        createExample('Delete a directory recursively', { path: './temp-dir', recursive: true, force: true }, 'Deletes the directory and all its contents'),
    ];
    getParameterSchema() {
        return z.object({
            path: z.string().min(1),
            recursive: z.boolean().default(false),
            force: z.boolean().default(false),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { path: filePath, recursive, force } = parameters;
            const resolvedPath = path.resolve(context.workingDirectory, filePath);
            // Security check
            if (!resolvedPath.startsWith(context.workingDirectory)) {
                return this.createErrorResult('Access denied: Path is outside working directory', {}, Date.now() - startTime);
            }
            const stats = await fs.stat(resolvedPath);
            if (stats.isDirectory()) {
                if (!recursive) {
                    return this.createErrorResult('Cannot delete directory without recursive flag', {}, Date.now() - startTime);
                }
                await fs.rmdir(resolvedPath, { recursive: true });
            }
            else {
                await fs.unlink(resolvedPath);
            }
            return this.createSuccessResult('File/directory deleted successfully', {
                path: resolvedPath,
                type: stats.isDirectory() ? 'directory' : 'file',
                recursive,
                force,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`Failed to delete: ${error instanceof Error ? error.message : String(error)}`, {}, Date.now() - startTime);
        }
    }
}
//# sourceMappingURL=file-operations.js.map