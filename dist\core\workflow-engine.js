import { nanoid } from 'nanoid';
import { logger } from '../utils/logger.js';
import { settings } from '../config/settings.js';
import { functionRegistry } from './function-registry.js';
import { deepSeekClient } from './ai-client.js';
class WorkflowEngine {
    workflows = new Map();
    executionQueue = [];
    isProcessing = false;
    // Create a new workflow
    createWorkflow(name, description, context = {}) {
        const workflow = {
            id: nanoid(),
            name,
            description,
            steps: [],
            context: {
                sessionId: nanoid(),
                workingDirectory: process.cwd(),
                environment: Object.fromEntries(Object.entries(process.env).filter(([_, value]) => value !== undefined)),
                variables: {},
                metadata: {},
                ...context,
            },
            status: 'created',
            progress: {
                current: 0,
                total: 0,
                percentage: 0,
            },
        };
        this.workflows.set(workflow.id, workflow);
        logger.info(`Created workflow: ${workflow.name} (${workflow.id})`);
        return workflow;
    }
    // Add a step to a workflow
    addStep(workflowId, name, description, functionCall, options = {}) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) {
            throw new Error(`Workflow not found: ${workflowId}`);
        }
        const step = {
            id: nanoid(),
            name,
            description,
            functionCall,
            condition: options.condition,
            retryCount: 0,
            maxRetries: options.maxRetries || settings.settings.workflow.maxRetries,
            status: 'pending',
        };
        workflow.steps.push(step);
        workflow.progress.total = workflow.steps.length;
        logger.info(`Added step to workflow ${workflowId}: ${name}`);
        return step;
    }
    // Execute a workflow
    async executeWorkflow(workflowId, options = {}) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow) {
            throw new Error(`Workflow not found: ${workflowId}`);
        }
        const executionOptions = {
            continueOnError: false,
            parallelExecution: false,
            saveProgress: settings.settings.workflow.autoSave,
            maxConcurrency: 3,
            ...options,
        };
        workflow.status = 'running';
        workflow.startTime = new Date();
        workflow.progress.current = 0;
        workflow.progress.percentage = 0;
        logger.logWorkflowStep('workflow_start', {
            workflowId,
            name: workflow.name,
            stepCount: workflow.steps.length,
        });
        try {
            if (executionOptions.parallelExecution) {
                await this.executeStepsParallel(workflow, executionOptions);
            }
            else {
                await this.executeStepsSequential(workflow, executionOptions);
            }
            workflow.status = 'completed';
            workflow.endTime = new Date();
            workflow.duration = workflow.endTime.getTime() - workflow.startTime.getTime();
            workflow.progress.percentage = 100;
            logger.logWorkflowStep('workflow_complete', {
                workflowId,
                duration: workflow.duration,
                status: workflow.status,
            });
        }
        catch (error) {
            workflow.status = 'failed';
            workflow.endTime = new Date();
            workflow.duration = workflow.endTime.getTime() - workflow.startTime.getTime();
            logger.error(`Workflow execution failed: ${workflowId}`, error);
            throw error;
        }
        return workflow;
    }
    // Execute steps sequentially
    async executeStepsSequential(workflow, options) {
        for (const step of workflow.steps) {
            if (step.condition && !this.evaluateCondition(step.condition, workflow.context)) {
                step.status = 'skipped';
                logger.info(`Skipped step: ${step.name} (condition not met)`);
                continue;
            }
            const success = await this.executeStep(workflow, step);
            workflow.progress.current++;
            workflow.progress.percentage = Math.round((workflow.progress.current / workflow.progress.total) * 100);
            if (!success && !options.continueOnError) {
                throw new Error(`Step failed: ${step.name}`);
            }
            if (options.saveProgress) {
                await this.saveWorkflowProgress(workflow);
            }
        }
    }
    // Execute steps in parallel
    async executeStepsParallel(workflow, options) {
        const semaphore = new Semaphore(options.maxConcurrency);
        const promises = [];
        for (const step of workflow.steps) {
            if (step.condition && !this.evaluateCondition(step.condition, workflow.context)) {
                step.status = 'skipped';
                continue;
            }
            const promise = semaphore.acquire().then(async (release) => {
                try {
                    return await this.executeStep(workflow, step);
                }
                finally {
                    release();
                    workflow.progress.current++;
                    workflow.progress.percentage = Math.round((workflow.progress.current / workflow.progress.total) * 100);
                }
            });
            promises.push(promise);
        }
        const results = await Promise.all(promises);
        if (!options.continueOnError && results.some(result => !result)) {
            throw new Error('One or more steps failed');
        }
    }
    // Execute a single step
    async executeStep(workflow, step) {
        step.status = 'running';
        step.startTime = new Date();
        logger.logWorkflowStep('step_start', {
            workflowId: workflow.id,
            stepId: step.id,
            stepName: step.name,
        });
        try {
            if (step.functionCall) {
                const result = await functionRegistry.executeFunction(step.functionCall, {
                    sessionId: workflow.context.sessionId,
                    userId: workflow.context.userId,
                    workingDirectory: workflow.context.workingDirectory,
                    environment: workflow.context.environment,
                    metadata: workflow.context.metadata,
                });
                step.result = result;
                if (result.success) {
                    step.status = 'completed';
                    // Store result in workflow variables for later steps
                    workflow.context.variables[`step_${step.id}_result`] = result.result;
                }
                else {
                    step.status = 'failed';
                    step.error = result.error;
                    // Retry logic
                    if (step.retryCount < step.maxRetries) {
                        step.retryCount++;
                        step.status = 'pending';
                        logger.warn(`Retrying step: ${step.name} (attempt ${step.retryCount}/${step.maxRetries})`);
                        // Wait before retry
                        await new Promise(resolve => setTimeout(resolve, settings.settings.workflow.retryDelay));
                        return this.executeStep(workflow, step);
                    }
                }
            }
            else {
                // Step without function call (placeholder or manual step)
                step.status = 'completed';
                step.result = { message: 'Manual step completed' };
            }
            step.endTime = new Date();
            step.duration = step.endTime.getTime() - step.startTime.getTime();
            logger.logWorkflowStep('step_complete', {
                workflowId: workflow.id,
                stepId: step.id,
                stepName: step.name,
                status: step.status,
                duration: step.duration,
            });
            return step.status === 'completed';
        }
        catch (error) {
            step.status = 'failed';
            step.error = error instanceof Error ? error.message : String(error);
            step.endTime = new Date();
            step.duration = step.endTime.getTime() - step.startTime.getTime();
            logger.error(`Step execution failed: ${step.name}`, error);
            return false;
        }
    }
    // Evaluate step condition
    evaluateCondition(condition, context) {
        try {
            // Simple condition evaluation - can be enhanced with a proper expression parser
            // For now, support basic variable checks
            const variableMatch = condition.match(/\$\{(\w+)\}/);
            if (variableMatch) {
                const variableName = variableMatch[1];
                const value = context.variables[variableName];
                return Boolean(value);
            }
            // Default to true for unknown conditions
            return true;
        }
        catch (error) {
            logger.warn(`Failed to evaluate condition: ${condition}`, error);
            return true;
        }
    }
    // Save workflow progress
    async saveWorkflowProgress(workflow) {
        try {
            // Implementation for saving workflow state
            // Could save to file, database, or other persistence layer
            logger.debug(`Saving workflow progress: ${workflow.id}`);
        }
        catch (error) {
            logger.warn(`Failed to save workflow progress: ${workflow.id}`, error);
        }
    }
    // Get workflow by ID
    getWorkflow(workflowId) {
        return this.workflows.get(workflowId);
    }
    // List all workflows
    listWorkflows() {
        return Array.from(this.workflows.values());
    }
    // Delete workflow
    deleteWorkflow(workflowId) {
        const workflow = this.workflows.get(workflowId);
        if (workflow && workflow.status !== 'running') {
            this.workflows.delete(workflowId);
            logger.info(`Deleted workflow: ${workflowId}`);
            return true;
        }
        return false;
    }
    // Pause workflow execution
    pauseWorkflow(workflowId) {
        const workflow = this.workflows.get(workflowId);
        if (workflow && workflow.status === 'running') {
            workflow.status = 'paused';
            logger.info(`Paused workflow: ${workflowId}`);
            return true;
        }
        return false;
    }
    // Resume workflow execution
    async resumeWorkflow(workflowId) {
        const workflow = this.workflows.get(workflowId);
        if (!workflow || workflow.status !== 'paused') {
            throw new Error(`Cannot resume workflow: ${workflowId}`);
        }
        workflow.status = 'running';
        logger.info(`Resumed workflow: ${workflowId}`);
        // Continue execution from where it left off
        return this.executeWorkflow(workflowId);
    }
    // Generate workflow from AI planning
    async generateWorkflowFromPrompt(prompt, context = {}) {
        const planningMessages = [
            {
                role: 'system',
                content: `You are a workflow planning assistant. Given a user request, break it down into a series of actionable steps that can be executed using available tools. 

Available tools: ${functionRegistry.getAllTools().map(tool => `${tool.name}: ${tool.description}`).join(', ')}

Respond with a JSON object containing:
- name: workflow name
- description: workflow description  
- steps: array of steps with name, description, and functionCall (if applicable)

Example:
{
  "name": "File Analysis Workflow",
  "description": "Analyze files in a directory",
  "steps": [
    {
      "name": "List Files",
      "description": "Get list of files in directory",
      "functionCall": {
        "name": "directory_list",
        "arguments": {"path": "./", "recursive": true}
      }
    }
  ]
}`
            },
            {
                role: 'user',
                content: prompt,
            },
        ];
        try {
            const response = await deepSeekClient.chatWithReasoning(planningMessages);
            const plan = JSON.parse(response.content);
            const workflow = this.createWorkflow(plan.name, plan.description, context);
            for (const stepDef of plan.steps) {
                this.addStep(workflow.id, stepDef.name, stepDef.description, stepDef.functionCall);
            }
            logger.info(`Generated workflow from prompt: ${workflow.name}`);
            return workflow;
        }
        catch (error) {
            logger.error('Failed to generate workflow from prompt', error);
            throw new Error(`Workflow generation failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
// Semaphore for controlling concurrency
class Semaphore {
    permits;
    queue = [];
    constructor(permits) {
        this.permits = permits;
    }
    async acquire() {
        return new Promise((resolve) => {
            if (this.permits > 0) {
                this.permits--;
                resolve(() => this.release());
            }
            else {
                this.queue.push(() => {
                    this.permits--;
                    resolve(() => this.release());
                });
            }
        });
    }
    release() {
        this.permits++;
        if (this.queue.length > 0) {
            const next = this.queue.shift();
            next();
        }
    }
}
export const workflowEngine = new WorkflowEngine();
export default workflowEngine;
//# sourceMappingURL=workflow-engine.js.map