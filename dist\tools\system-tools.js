import { exec } from 'child_process';
import { promisify } from 'util';
import * as os from 'os';
import { z } from 'zod';
import { BaseTool, createParameter, createExample } from './base-tool.js';
const execAsync = promisify(exec);
// System Command Tool
export class SystemCommandTool extends BaseTool {
    name = 'system_command';
    description = 'Execute system commands safely with output capture';
    parameters = {
        type: 'object',
        properties: {
            command: createParameter('string', 'Command to execute'),
            timeout: createParameter('number', 'Timeout in milliseconds (default: 30000)'),
            cwd: createParameter('string', 'Working directory for command execution (optional)'),
            env: createParameter('object', 'Environment variables to set (optional)', {
                properties: {},
            }),
        },
        required: ['command'],
    };
    examples = [
        createExample('List directory contents', { command: 'ls -la' }, 'Returns directory listing with file details'),
        createExample('Get system information', { command: 'uname -a', timeout: 5000 }, 'Returns system information with custom timeout'),
    ];
    getParameterSchema() {
        return z.object({
            command: z.string().min(1),
            timeout: z.number().positive().default(30000),
            cwd: z.string().optional(),
            env: z.record(z.string()).optional(),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { command, timeout, cwd, env } = parameters;
            // Security check - prevent dangerous commands
            if (this.isDangerousCommand(command)) {
                return this.createErrorResult('Command blocked for security reasons', { command }, Date.now() - startTime);
            }
            const options = {
                timeout,
                cwd: cwd || context.workingDirectory,
                env: { ...process.env, ...context.environment, ...env },
                maxBuffer: 1024 * 1024, // 1MB buffer
            };
            const { stdout, stderr } = await execAsync(command, options);
            return this.createSuccessResult({
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                command,
                exitCode: 0,
            }, {
                command,
                cwd: options.cwd,
                timeout,
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`Command execution failed: ${error instanceof Error ? error.message : String(error)}`, {
                command: parameters.command,
                exitCode: error.code || -1,
                stderr: error.stderr || '',
            }, Date.now() - startTime);
        }
    }
    isDangerousCommand(command) {
        const dangerousPatterns = [
            /rm\s+-rf\s+\//, // rm -rf /
            /sudo\s+rm/, // sudo rm
            /mkfs/, // format filesystem
            /dd\s+if=/, // disk operations
            /shutdown/, // system shutdown
            /reboot/, // system reboot
            /halt/, // system halt
            /init\s+0/, // shutdown
            /init\s+6/, // reboot
            /:\(\)\{.*\}/, // fork bomb
        ];
        return dangerousPatterns.some(pattern => pattern.test(command.toLowerCase()));
    }
}
// System Information Tool
export class SystemInfoTool extends BaseTool {
    name = 'system_info';
    description = 'Get comprehensive system information';
    parameters = {
        type: 'object',
        properties: {
            detailed: createParameter('boolean', 'Include detailed system information (default: false)'),
            sections: createParameter('array', 'Specific sections to include', {
                items: createParameter('string', 'Section name', {
                    enum: ['os', 'cpu', 'memory', 'network', 'disk', 'processes'],
                }),
            }),
        },
        required: [],
    };
    examples = [
        createExample('Basic system info', { detailed: false }, 'Returns basic OS, CPU, and memory information'),
        createExample('Detailed CPU and memory info', { detailed: true, sections: ['cpu', 'memory'] }, 'Returns detailed CPU and memory statistics'),
    ];
    getParameterSchema() {
        return z.object({
            detailed: z.boolean().default(false),
            sections: z.array(z.enum(['os', 'cpu', 'memory', 'network', 'disk', 'processes'])).optional(),
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { detailed, sections } = parameters;
            const info = {};
            const sectionsToInclude = sections || ['os', 'cpu', 'memory'];
            if (sectionsToInclude.includes('os')) {
                info.os = {
                    platform: os.platform(),
                    type: os.type(),
                    release: os.release(),
                    version: os.version(),
                    arch: os.arch(),
                    hostname: os.hostname(),
                    uptime: os.uptime(),
                };
            }
            if (sectionsToInclude.includes('cpu')) {
                const cpus = os.cpus();
                info.cpu = {
                    model: cpus[0]?.model || 'Unknown',
                    cores: cpus.length,
                    speed: cpus[0]?.speed || 0,
                    ...(detailed && {
                        details: cpus.map(cpu => ({
                            model: cpu.model,
                            speed: cpu.speed,
                            times: cpu.times,
                        })),
                    }),
                };
            }
            if (sectionsToInclude.includes('memory')) {
                const totalMem = os.totalmem();
                const freeMem = os.freemem();
                const usedMem = totalMem - freeMem;
                info.memory = {
                    total: totalMem,
                    free: freeMem,
                    used: usedMem,
                    usagePercent: Math.round((usedMem / totalMem) * 100),
                    totalGB: Math.round(totalMem / (1024 ** 3) * 100) / 100,
                    freeGB: Math.round(freeMem / (1024 ** 3) * 100) / 100,
                    usedGB: Math.round(usedMem / (1024 ** 3) * 100) / 100,
                };
            }
            if (sectionsToInclude.includes('network')) {
                const networkInterfaces = os.networkInterfaces();
                info.network = Object.entries(networkInterfaces).reduce((acc, [name, interfaces]) => {
                    acc[name] = interfaces?.map(iface => ({
                        address: iface.address,
                        family: iface.family,
                        internal: iface.internal,
                        ...(detailed && {
                            netmask: iface.netmask,
                            mac: iface.mac,
                            cidr: iface.cidr,
                        }),
                    })) || [];
                    return acc;
                }, {});
            }
            if (sectionsToInclude.includes('disk') && detailed) {
                // Note: Disk information requires additional system calls
                try {
                    const { stdout } = await execAsync('df -h');
                    info.disk = {
                        usage: stdout.trim(),
                    };
                }
                catch (error) {
                    info.disk = { error: 'Unable to retrieve disk information' };
                }
            }
            if (sectionsToInclude.includes('processes') && detailed) {
                try {
                    const { stdout } = await execAsync('ps aux | head -20');
                    info.processes = {
                        top20: stdout.trim(),
                    };
                }
                catch (error) {
                    info.processes = { error: 'Unable to retrieve process information' };
                }
            }
            return this.createSuccessResult(info, {
                detailed,
                sections: sectionsToInclude,
                timestamp: new Date().toISOString(),
            }, Date.now() - startTime);
        }
        catch (error) {
            return this.createErrorResult(`Failed to get system information: ${error instanceof Error ? error.message : String(error)}`, {}, Date.now() - startTime);
        }
    }
}
// Environment Variables Tool
export class EnvironmentTool extends BaseTool {
    name = 'environment_vars';
    description = 'Get and manage environment variables';
    parameters = {
        type: 'object',
        properties: {
            action: createParameter('string', 'Action to perform', {
                enum: ['get', 'list', 'set'],
            }),
            variable: createParameter('string', 'Environment variable name (for get/set actions)'),
            value: createParameter('string', 'Value to set (for set action)'),
            filter: createParameter('string', 'Filter pattern for list action (optional)'),
        },
        required: ['action'],
    };
    examples = [
        createExample('Get specific environment variable', { action: 'get', variable: 'PATH' }, 'Returns the value of the PATH environment variable'),
        createExample('List all environment variables', { action: 'list' }, 'Returns all environment variables'),
        createExample('List filtered environment variables', { action: 'list', filter: 'NODE' }, 'Returns environment variables containing "NODE"'),
    ];
    getParameterSchema() {
        return z.object({
            action: z.enum(['get', 'list', 'set']),
            variable: z.string().optional(),
            value: z.string().optional(),
            filter: z.string().optional(),
        }).refine((data) => {
            if (data.action === 'get' && !data.variable) {
                return false;
            }
            if (data.action === 'set' && (!data.variable || !data.value)) {
                return false;
            }
            return true;
        }, {
            message: 'Variable name required for get/set actions, variable and value required for set action',
        });
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            const { action, variable, value, filter } = parameters;
            switch (action) {
                case 'get':
                    const envValue = process.env[variable];
                    return this.createSuccessResult({
                        variable,
                        value: envValue || null,
                        exists: envValue !== undefined,
                    }, { action, variable }, Date.now() - startTime);
                case 'list':
                    let envVars = { ...process.env };
                    if (filter) {
                        const filterRegex = new RegExp(filter, 'i');
                        envVars = Object.fromEntries(Object.entries(envVars).filter(([key]) => filterRegex.test(key)));
                    }
                    return this.createSuccessResult({
                        variables: envVars,
                        count: Object.keys(envVars).length,
                    }, { action, filter }, Date.now() - startTime);
                case 'set':
                    // Note: This only sets for the current process
                    process.env[variable] = value;
                    return this.createSuccessResult({
                        variable,
                        value,
                        set: true,
                        note: 'Variable set for current process only',
                    }, { action, variable }, Date.now() - startTime);
                default:
                    return this.createErrorResult(`Unknown action: ${action}`, { action }, Date.now() - startTime);
            }
        }
        catch (error) {
            return this.createErrorResult(`Environment operation failed: ${error instanceof Error ? error.message : String(error)}`, { action: parameters.action }, Date.now() - startTime);
        }
    }
}
//# sourceMappingURL=system-tools.js.map