import { ChatMessage } from './ai-client.js';
export interface SessionData {
    id: string;
    userId?: string;
    name: string;
    description?: string;
    createdAt: Date;
    lastAccessedAt: Date;
    messages: ChatMessage[];
    context: Record<string, any>;
    metadata: Record<string, any>;
    workflowIds: string[];
    totalMessages: number;
    totalTokensUsed: number;
}
export interface SessionSummary {
    id: string;
    name: string;
    description?: string;
    createdAt: Date;
    lastAccessedAt: Date;
    messageCount: number;
    workflowCount: number;
}
export interface SessionSearchOptions {
    query?: string;
    userId?: string;
    dateFrom?: Date;
    dateTo?: Date;
    limit?: number;
    sortBy?: 'createdAt' | 'lastAccessedAt' | 'messageCount';
    sortOrder?: 'asc' | 'desc';
}
declare class SessionManager {
    private sessions;
    private currentSessionId;
    private sessionsDirectory;
    private maxSessions;
    private autoSaveInterval;
    constructor();
    createSession(name: string, options?: {
        description?: string;
        userId?: string;
        context?: Record<string, any>;
        metadata?: Record<string, any>;
    }): Promise<SessionData>;
    getCurrentSession(): Promise<SessionData>;
    switchSession(sessionId: string): Promise<SessionData | null>;
    addMessage(message: ChatMessage, tokensUsed?: number): Promise<void>;
    getSessionMessages(sessionId?: string, options?: {
        limit?: number;
        fromDate?: Date;
        role?: 'user' | 'assistant' | 'system';
    }): ChatMessage[];
    updateSessionContext(sessionId: string, context: Record<string, any>): Promise<void>;
    addWorkflowToSession(sessionId: string, workflowId: string): Promise<void>;
    listSessions(options?: SessionSearchOptions): Promise<SessionSummary[]>;
    deleteSession(sessionId: string): Promise<boolean>;
    exportSession(sessionId: string): Promise<string>;
    importSession(sessionData: string): Promise<SessionData>;
    getSessionStats(): {
        totalSessions: number;
        totalMessages: number;
        totalTokensUsed: number;
        averageMessagesPerSession: number;
        oldestSession?: Date;
        newestSession?: Date;
    };
    cleanupOldSessions(maxAge?: number): Promise<number>;
    private ensureSessionsDirectory;
    private saveSession;
    private loadSession;
    private loadAllSessions;
    private startAutoSave;
    destroy(): void;
}
export declare const sessionManager: SessionManager;
export default sessionManager;
//# sourceMappingURL=session-manager.d.ts.map