import { FunctionCall } from './function-registry.js';
export interface WorkflowStep {
    id: string;
    name: string;
    description: string;
    functionCall?: FunctionCall;
    condition?: string;
    retryCount: number;
    maxRetries: number;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
    result?: any;
    error?: string;
    startTime?: Date;
    endTime?: Date;
    duration?: number;
}
export interface WorkflowContext {
    sessionId: string;
    userId?: string;
    workingDirectory: string;
    environment: Record<string, string>;
    variables: Record<string, any>;
    metadata: Record<string, any>;
}
export interface Workflow {
    id: string;
    name: string;
    description: string;
    steps: WorkflowStep[];
    context: WorkflowContext;
    status: 'created' | 'running' | 'completed' | 'failed' | 'paused';
    startTime?: Date;
    endTime?: Date;
    duration?: number;
    progress: {
        current: number;
        total: number;
        percentage: number;
    };
}
export interface WorkflowExecutionOptions {
    continueOnError: boolean;
    parallelExecution: boolean;
    saveProgress: boolean;
    maxConcurrency: number;
}
declare class WorkflowEngine {
    private workflows;
    private executionQueue;
    private isProcessing;
    createWorkflow(name: string, description: string, context?: Partial<WorkflowContext>): Workflow;
    addStep(workflowId: string, name: string, description: string, functionCall?: FunctionCall, options?: {
        condition?: string;
        maxRetries?: number;
    }): WorkflowStep;
    executeWorkflow(workflowId: string, options?: Partial<WorkflowExecutionOptions>): Promise<Workflow>;
    private executeStepsSequential;
    private executeStepsParallel;
    private executeStep;
    private evaluateCondition;
    private saveWorkflowProgress;
    getWorkflow(workflowId: string): Workflow | undefined;
    listWorkflows(): Workflow[];
    deleteWorkflow(workflowId: string): boolean;
    pauseWorkflow(workflowId: string): boolean;
    resumeWorkflow(workflowId: string): Promise<Workflow>;
    generateWorkflowFromPrompt(prompt: string, context?: Partial<WorkflowContext>): Promise<Workflow>;
}
export declare const workflowEngine: WorkflowEngine;
export default workflowEngine;
//# sourceMappingURL=workflow-engine.d.ts.map