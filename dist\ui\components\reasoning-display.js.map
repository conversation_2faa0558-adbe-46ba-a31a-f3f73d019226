{"version": 3, "file": "reasoning-display.js", "sourceRoot": "", "sources": ["../../../src/ui/components/reasoning-display.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,KAAK,MAAM,OAAO,CAAC;AAY1B,MAAM,OAAO,gBAAgB;IACnB,OAAO,CAA0B;IACjC,KAAK,GAAoB,EAAE,CAAC;IAC5B,YAAY,GAAG,KAAK,CAAC;IAE7B,YAAY,UAA4C,EAAE;QACxD,IAAI,CAAC,OAAO,GAAG;YACb,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,IAAI;YACrB,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,KAAK;YACd,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED,6DAA6D;IAC7D,OAAO,CAAC,IAAmB;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED,kCAAkC;IAClC,WAAW,CAAC,IAAmB;QAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;IAED,8BAA8B;IAC9B,eAAe;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAE9B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC1B,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,4BAA4B;YAC7C,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,iCAAiC;IACzB,UAAU,CAAC,IAAmB;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,cAAc;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YACjC,MAAM,UAAU,GAAI,KAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YAC3E,MAAM,IAAI,UAAU,GAAG,GAAG,CAAC;QAC7B,CAAC;QAED,YAAY;QACZ,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,SAAS,GAAI,KAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;YAC9F,MAAM,IAAI,SAAS,GAAG,GAAG,CAAC;QAC5B,CAAC;QAED,UAAU;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;QACxE,MAAM,gBAAgB,GAAI,KAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,gBAAgB,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,0CAA0C;IAClC,QAAQ;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO;gBACL,UAAU,EAAE,MAAM;gBAClB,SAAS,EAAE,MAAM;gBACjB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;aACf,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,UAAU,EAAE,MAAM;gBAClB,SAAS,EAAE,MAAM;gBACjB,OAAO,EAAE,OAAO;gBAChB,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;aACf,CAAC;QACJ,CAAC;IACH,CAAC;IAED,mCAAmC;IAC3B,SAAS;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAI,KAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAE3E,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC,KAAK,EAAE;YAClB,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,CAAC;YACT,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,KAAK,CAAC,MAAM;YACzB,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;IACL,CAAC;IAED,mCAAmC;IAC3B,SAAS;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,OAAO,GAAI,KAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,yBAAyB,IAAI,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC,CAAC;QAElG,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,KAAK,CAAC,OAAO,EAAE;YACpB,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;YAChD,WAAW,EAAE,QAAQ;YACrB,WAAW,EAAE,KAAK,CAAC,MAAM;YACzB,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;IACL,CAAC;IAED,+BAA+B;IACvB,QAAQ,CAAC,IAAY,EAAE,KAAa;QAC1C,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;gBACzC,WAAW,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,IAAI,WAAW,EAAE,CAAC;oBAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC1B,CAAC;gBACD,WAAW,GAAG,IAAI,CAAC;YACrB,CAAC;QACH,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,4CAA4C;IACpC,UAAU,CAAC,IAAY,EAAE,MAAc;QAC7C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChE,CAAC;IAED,kBAAkB;IAClB,KAAK;QACH,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAED,iBAAiB;IACjB,YAAY;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,gBAAgB;IAChB,QAAQ;QACN,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,iCAAiC;IACjC,YAAY;QACV,IAAI,MAAM,GAAG,wBAAwB,CAAC;QACtC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QAElC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,MAAM,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;YACnE,MAAM,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAClC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,uBAAuB;IACvB,YAAY;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YAC5B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IAED,sCAAsC;IACtC,cAAc;QACZ,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,kCAAkC;IAClC,aAAa;QACX,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED,yBAAyB;IACzB,aAAa,CAAC,OAAyC;QACrD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;IACjD,CAAC;CACF;AAED,qEAAqE;AACrE,MAAM,UAAU,sBAAsB,CAAC,MAAyC;IAC9E,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,SAAS;YACZ,OAAO,IAAI,gBAAgB,CAAC;gBAC1B,cAAc,EAAE,IAAI;gBACpB,eAAe,EAAE,IAAI;gBACrB,QAAQ,EAAE,GAAG;gBACb,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;QAEL,KAAK,SAAS;YACZ,OAAO,IAAI,gBAAgB,CAAC;gBAC1B,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,IAAI;gBACrB,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;QAEL,KAAK,SAAS;YACZ,OAAO,IAAI,gBAAgB,CAAC;gBAC1B,cAAc,EAAE,KAAK;gBACrB,eAAe,EAAE,KAAK;gBACtB,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;QAEL;YACE,OAAO,IAAI,gBAAgB,EAAE,CAAC;IAClC,CAAC;AACH,CAAC"}